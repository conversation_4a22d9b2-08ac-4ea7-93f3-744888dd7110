plugins {
    java
    id("org.springframework.boot") version "3.5.0"
    id("io.spring.dependency-management") version "1.1.7"
}

group = "com.cbs"
version = "0.0.1-SNAPSHOT"

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(24)
    }
}

repositories {
    mavenCentral()
}

extra["springAiVersion"] = "1.0.0"

dependencies {
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-aop")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("org.springframework.boot:spring-boot-starter-mail")
    implementation("org.springframework.boot:spring-boot-starter-validation")
    implementation("org.springframework.boot:spring-boot-starter-webflux")
    implementation("org.springframework.boot:spring-boot-starter-thymeleaf")
//    implementation("io.micrometer:micrometer-tracing-bridge-brave")
//    implementation("io.zipkin.reporter2:zipkin-reporter-brave")

    // Google Generative AI (direct API, not Vertex AI)
    implementation("com.google.genai:google-genai:1.0.0")

    // Spring AI OpenAI integration - for OpenAI and OpenAI-compatible APIs (DeepSeek)
    implementation("org.springframework.ai:spring-ai-starter-model-openai")

    // Spring AI Ollama integration
    implementation("org.springframework.ai:spring-ai-ollama")
    implementation("org.springframework.ai:spring-ai-autoconfigure-model-ollama")
    
    // Spring AI MCP Server with WebFlux SSE transport
    implementation("org.springframework.ai:spring-ai-starter-mcp-server-webflux")
    
    // Spring AI MCP Client for testing our server
    implementation("org.springframework.ai:spring-ai-starter-mcp-client-webflux")

    // Swagger/OpenAPI Documentation
    implementation("org.springdoc:springdoc-openapi-starter-webflux-ui:2.7.0")

    developmentOnly("org.springframework.boot:spring-boot-devtools")
    runtimeOnly("com.mysql:mysql-connector-j")
    runtimeOnly("io.micrometer:micrometer-registry-prometheus")
    
    // Enterprise Logging Dependencies
    implementation("net.logstash.logback:logstash-logback-encoder:7.4")
    implementation("ch.qos.logback:logback-classic")
    implementation("org.springframework.boot:spring-boot-starter-aop")
    
    // HTML Parsing for ShareSansar provider
    implementation("org.jsoup:jsoup:1.17.2")
    
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("io.projectreactor:reactor-test")
    testImplementation("org.junit.platform:junit-platform-suite-api")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")
    testRuntimeOnly("org.junit.platform:junit-platform-suite-engine")
}

dependencyManagement {
    imports {
        mavenBom("org.springframework.ai:spring-ai-bom:${property("springAiVersion")}")
    }
}

tasks.withType<Test> {
    useJUnitPlatform()
}
