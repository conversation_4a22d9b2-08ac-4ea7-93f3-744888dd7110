package com.cbs.nepsetms.service.ai;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Base64;

/**
 * Test class to verify if DeepSeek API supports vision/image analysis.
 * This is an experimental test to check if we can send images to DeepSeek API.
 */
public class DeepSeekVisionTest {
    
    private static final Logger logger = LoggerFactory.getLogger(DeepSeekVisionTest.class);
    private static final String DEEPSEEK_API_URL = "https://api.deepseek.com/chat/completions";
    
    // Test with a small base64 encoded image (1x1 pixel PNG)
    private static final String TEST_IMAGE_BASE64 = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==";
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * Test if DeepSeek API supports vision by sending an image with text.
     * This test will help us determine if we can use DeepSeek for CAPTCHA solving.
     */
    @Test
    public void testDeepSeekVisionSupport() {
        // Skip test if no API key is available
        String apiKey = System.getenv("DEEPSEEK_API_KEY");
        if (apiKey == null || apiKey.trim().isEmpty() || apiKey.equals("your-deepseek-api-key")) {
            logger.info("⏭️ Skipping DeepSeek vision test - no API key configured");
            return;
        }
        
        logger.info("🧪 Testing DeepSeek API vision support...");
        
        try {
            // Test 1: Try with OpenAI-style vision format
            testOpenAIStyleVision(apiKey);
            
            // Test 2: Try with simple base64 format
            testSimpleBase64Format(apiKey);
            
            // Test 3: Try with different model names
            testDifferentModels(apiKey);
            
        } catch (Exception e) {
            logger.error("❌ DeepSeek vision test failed", e);
        }
    }
    
    /**
     * Test OpenAI-style vision format (most likely to work if supported)
     */
    private void testOpenAIStyleVision(String apiKey) {
        logger.info("🔍 Testing OpenAI-style vision format...");
        
        try {
            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("model", "deepseek-chat");
            
            // Create messages array
            ArrayNode messages = objectMapper.createArrayNode();
            
            // User message with image
            ObjectNode userMessage = objectMapper.createObjectNode();
            userMessage.put("role", "user");
            
            // Content array with text and image
            ArrayNode content = objectMapper.createArrayNode();
            
            // Text content
            ObjectNode textContent = objectMapper.createObjectNode();
            textContent.put("type", "text");
            textContent.put("text", "What do you see in this image?");
            content.add(textContent);
            
            // Image content
            ObjectNode imageContent = objectMapper.createObjectNode();
            imageContent.put("type", "image_url");
            ObjectNode imageUrl = objectMapper.createObjectNode();
            imageUrl.put("url", "data:image/png;base64," + TEST_IMAGE_BASE64);
            imageContent.set("image_url", imageUrl);
            content.add(imageContent);
            
            userMessage.set("content", content);
            messages.add(userMessage);
            
            requestBody.set("messages", messages);
            requestBody.put("max_tokens", 100);
            
            // Send request
            String response = sendRequest(apiKey, requestBody);
            logger.info("✅ OpenAI-style format response: {}", response);
            
        } catch (Exception e) {
            logger.warn("❌ OpenAI-style format failed: {}", e.getMessage());
        }
    }
    
    /**
     * Test simple base64 format
     */
    private void testSimpleBase64Format(String apiKey) {
        logger.info("🔍 Testing simple base64 format...");
        
        try {
            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("model", "deepseek-chat");
            
            // Create messages array
            ArrayNode messages = objectMapper.createArrayNode();
            
            // User message with embedded image
            ObjectNode userMessage = objectMapper.createObjectNode();
            userMessage.put("role", "user");
            userMessage.put("content", "![](data:image/png;base64," + TEST_IMAGE_BASE64 + ") What do you see in this image?");
            messages.add(userMessage);
            
            requestBody.set("messages", messages);
            requestBody.put("max_tokens", 100);
            
            // Send request
            String response = sendRequest(apiKey, requestBody);
            logger.info("✅ Simple base64 format response: {}", response);
            
        } catch (Exception e) {
            logger.warn("❌ Simple base64 format failed: {}", e.getMessage());
        }
    }
    
    /**
     * Test different model names that might support vision
     */
    private void testDifferentModels(String apiKey) {
        String[] modelsToTest = {
            "deepseek-vl",
            "deepseek-vl-chat", 
            "deepseek-vl2",
            "deepseek-vl2-small",
            "deepseek-vision"
        };
        
        for (String model : modelsToTest) {
            logger.info("🔍 Testing model: {}", model);
            
            try {
                ObjectNode requestBody = objectMapper.createObjectNode();
                requestBody.put("model", model);
                
                // Create simple text message first to see if model exists
                ArrayNode messages = objectMapper.createArrayNode();
                ObjectNode userMessage = objectMapper.createObjectNode();
                userMessage.put("role", "user");
                userMessage.put("content", "Hello, can you see images?");
                messages.add(userMessage);
                
                requestBody.set("messages", messages);
                requestBody.put("max_tokens", 50);
                
                String response = sendRequest(apiKey, requestBody);
                logger.info("✅ Model {} response: {}", model, response);
                
            } catch (Exception e) {
                logger.warn("❌ Model {} failed: {}", model, e.getMessage());
            }
        }
    }
    
    /**
     * Send HTTP request to DeepSeek API
     */
    private String sendRequest(String apiKey, ObjectNode requestBody) {
        WebClient webClient = WebClient.builder()
            .baseUrl(DEEPSEEK_API_URL)
            .defaultHeader("Authorization", "Bearer " + apiKey)
            .defaultHeader("Content-Type", "application/json")
            .build();
        
        return webClient.post()
            .bodyValue(requestBody)
            .retrieve()
            .bodyToMono(String.class)
            .timeout(Duration.ofSeconds(30))
            .block();
    }
    
    /**
     * Manual test method - run this to test with your actual API key
     */
    public static void main(String[] args) {
        DeepSeekVisionTest test = new DeepSeekVisionTest();
        test.testDeepSeekVisionSupport();
    }
}
