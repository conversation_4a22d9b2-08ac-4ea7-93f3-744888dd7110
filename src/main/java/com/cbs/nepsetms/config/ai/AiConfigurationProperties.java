package com.cbs.nepsetms.config.ai;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import java.util.Map;

/**
 * Configuration properties for AI services.
 */
@ConfigurationProperties(prefix = "nepse.ai")
@Validated
public class AiConfigurationProperties {

    private GeminiConfig gemini;
    private DeepSeekConfig deepseek;
    private OpenAiConfig openai;
    private HuggingFaceConfig huggingface;
    private CaptchaSolvingConfig captchaSolving;
    private String defaultProvider = "gemini";

    public GeminiConfig getGemini() {
        return gemini;
    }

    public void setGemini(GeminiConfig gemini) {
        this.gemini = gemini;
    }

    public DeepSeekConfig getDeepseek() {
        return deepseek;
    }

    public void setDeepseek(DeepSeekConfig deepseek) {
        this.deepseek = deepseek;
    }

    public OpenAiConfig getOpenai() {
        return openai;
    }

    public void setOpenai(OpenAiConfig openai) {
        this.openai = openai;
    }

    public HuggingFaceConfig getHuggingface() {
        return huggingface;
    }

    public void setHuggingface(HuggingFaceConfig huggingface) {
        this.huggingface = huggingface;
    }

    public CaptchaSolvingConfig getCaptchaSolving() {
        return captchaSolving;
    }

    public void setCaptchaSolving(CaptchaSolvingConfig captchaSolving) {
        this.captchaSolving = captchaSolving;
    }

    public String getDefaultProvider() {
        return defaultProvider;
    }

    public void setDefaultProvider(String defaultProvider) {
        this.defaultProvider = defaultProvider;
    }

    // For backward compatibility with record-style access
    public GeminiConfig gemini() {
        return gemini;
    }

    public DeepSeekConfig deepseek() {
        return deepseek;
    }

    public OpenAiConfig openai() {
        return openai;
    }

    public HuggingFaceConfig huggingface() {
        return huggingface;
    }

    public CaptchaSolvingConfig captchaSolving() {
        return captchaSolving;
    }

    public String defaultProvider() {
        return defaultProvider;
    }

    @Override
    public String toString() {
        return "AiConfigurationProperties[gemini=" + gemini +
               ", deepseek=" + deepseek + ", openai=" + openai +
               ", huggingface=" + huggingface +
               ", captchaSolving=" + captchaSolving + ", defaultProvider=" + defaultProvider + "]";
    }

    /**
     * Google Generative AI configuration.
     */
    public record GeminiConfig(
            boolean enabled,
            String apiKey,
            @NotBlank String model,
            @NotBlank String baseUrl
    ) {

        /**
         * Check if API key is configured.
         */
        public boolean hasApiKey() {
            return apiKey != null && !apiKey.trim().isEmpty();
        }

        /**
         * Check if service is enabled and has API key.
         */
        public boolean isAvailable() {
            return enabled && hasApiKey();
        }
    }

    /**
     * DeepSeek AI configuration (OpenAI-compatible).
     */
    public record DeepSeekConfig(
            boolean enabled,
            String apiKey,
            @NotBlank String model,
            @NotBlank String baseUrl
    ) {

        /**
         * Check if API key is configured.
         */
        public boolean hasApiKey() {
            return apiKey != null && !apiKey.trim().isEmpty();
        }

        /**
         * Check if service is enabled and has API key.
         */
        public boolean isAvailable() {
            return enabled && hasApiKey();
        }
    }

    /**
     * OpenAI configuration.
     */
    public record OpenAiConfig(
            boolean enabled,
            String apiKey,
            @NotBlank String model,
            @NotBlank String baseUrl
    ) {

        /**
         * Check if API key is configured.
         */
        public boolean hasApiKey() {
            return apiKey != null && !apiKey.trim().isEmpty();
        }

        /**
         * Check if service is enabled and has API key.
         */
        public boolean isAvailable() {
            return enabled && hasApiKey();
        }
    }

    /**
     * Hugging Face AI configuration.
     */
    public record HuggingFaceConfig(
            boolean enabled,
            String apiKey,
            @NotBlank String baseUrl,
            java.util.List<String> preferredModels
    ) {

        /**
         * Check if API key is configured.
         */
        public boolean hasApiKey() {
            return apiKey != null && !apiKey.trim().isEmpty() && !apiKey.equals("your-huggingface-api-key");
        }

        /**
         * Check if service is enabled and has API key.
         */
        public boolean isAvailable() {
            return enabled && hasApiKey();
        }
    }

    /**
     * CAPTCHA solving configuration.
     */
    public record CaptchaSolvingConfig(
            double temperature,
            @Positive int maxTokens,
            String systemPrompt,
            boolean enableFallback,
            int maxRetries,
            String preferredProvider
    ) {}
}
