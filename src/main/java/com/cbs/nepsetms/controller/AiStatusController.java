package com.cbs.nepsetms.controller;

import com.cbs.nepsetms.service.ai.AiServiceManager;
import com.cbs.nepsetms.service.ai.DeepSeekService;
import com.cbs.nepsetms.service.ai.GeminiService;
import com.cbs.nepsetms.service.ai.OllamaService;
import com.cbs.nepsetms.service.ai.OpenAiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Controller for checking AI service status and configuration.
 * Useful for debugging and monitoring AI provider availability.
 */
@RestController
@RequestMapping("/api/ai")
public class AiStatusController {

    private final AiServiceManager aiServiceManager;

    @Autowired(required = false)
    private GeminiService geminiService;

    @Autowired(required = false)
    private OllamaService ollamaService;

    @Autowired(required = false)
    private DeepSeekService deepSeekService;

    @Autowired(required = false)
    private OpenAiService openAiService;

    public AiStatusController(AiServiceManager aiServiceManager) {
        this.aiServiceManager = aiServiceManager;
    }

    /**
     * Get status of all AI providers.
     */
    @GetMapping("/status")
    public Map<String, Object> getAiStatus() {
        Map<String, Object> status = new HashMap<>();
        
        // Overall status
        status.put("availableProviders", aiServiceManager.getAvailableProviders());
        status.put("providerCapabilities", aiServiceManager.getProviderCapabilities());
        
        // Individual provider status
        Map<String, Object> providers = new HashMap<>();
        
        // Gemini
        if (geminiService != null) {
            Map<String, Object> geminiStatus = new HashMap<>();
            geminiStatus.put("available", geminiService.isAvailable());
            geminiStatus.put("providerName", geminiService.getProviderName());
            geminiStatus.put("capabilities", geminiService.getCapabilities());
            providers.put("gemini", geminiStatus);
        }
        
        // Ollama
        if (ollamaService != null) {
            Map<String, Object> ollamaStatus = new HashMap<>();
            ollamaStatus.put("available", ollamaService.isAvailable());
            ollamaStatus.put("providerName", ollamaService.getProviderName());
            ollamaStatus.put("capabilities", ollamaService.getCapabilities());
            providers.put("ollama", ollamaStatus);
        }
        
        // DeepSeek
        if (deepSeekService != null) {
            Map<String, Object> deepSeekStatus = new HashMap<>();
            deepSeekStatus.put("available", deepSeekService.isAvailable());
            deepSeekStatus.put("providerName", deepSeekService.getProviderName());
            deepSeekStatus.put("capabilities", deepSeekService.getCapabilities());
            deepSeekStatus.put("currentModel", deepSeekService.getCurrentModel());
            deepSeekStatus.put("modelInfo", deepSeekService.getModelInfo());
            deepSeekStatus.put("configInfo", deepSeekService.getConfigInfo());
            providers.put("deepseek", deepSeekStatus);
        }
        
        // OpenAI
        if (openAiService != null) {
            Map<String, Object> openAiStatus = new HashMap<>();
            openAiStatus.put("available", openAiService.isAvailable());
            openAiStatus.put("providerName", openAiService.getProviderName());
            openAiStatus.put("capabilities", openAiService.getCapabilities());
            openAiStatus.put("currentModel", openAiService.getCurrentModel());
            openAiStatus.put("modelInfo", openAiService.getModelInfo());
            openAiStatus.put("configInfo", openAiService.getConfigInfo());
            providers.put("openai", openAiStatus);
        }
        
        status.put("providers", providers);
        
        return status;
    }

    /**
     * Get quick availability check for each provider.
     */
    @GetMapping("/availability")
    public Map<String, Boolean> getProviderAvailability() {
        Map<String, Boolean> availability = new HashMap<>();
        
        availability.put("gemini", aiServiceManager.isGeminiAvailable());
        availability.put("ollama", aiServiceManager.isOllamaAvailable());
        availability.put("deepseek", aiServiceManager.isDeepSeekAvailable());
        availability.put("openai", aiServiceManager.isOpenAiAvailable());
        
        return availability;
    }

    /**
     * Test text generation with the default provider.
     */
    @GetMapping("/test-text")
    public Map<String, Object> testTextGeneration() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String testPrompt = "Say 'AI integration test successful' and nothing else.";
            String response = aiServiceManager.generateText(testPrompt).block();
            
            result.put("success", true);
            result.put("prompt", testPrompt);
            result.put("response", response);
            result.put("provider", "default");
        } catch (Exception e) {
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * Get configuration summary for debugging.
     */
    @GetMapping("/config")
    public Map<String, Object> getConfigSummary() {
        Map<String, Object> config = new HashMap<>();
        
        // DeepSeek config
        if (deepSeekService != null) {
            config.put("deepseek", deepSeekService.getConfigInfo());
        } else {
            config.put("deepseek", "Service not initialized");
        }
        
        // OpenAI config
        if (openAiService != null) {
            config.put("openai", openAiService.getConfigInfo());
        } else {
            config.put("openai", "Service not initialized");
        }
        
        return config;
    }
}
