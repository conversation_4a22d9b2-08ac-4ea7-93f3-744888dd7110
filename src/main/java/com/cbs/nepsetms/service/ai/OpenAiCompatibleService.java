package com.cbs.nepsetms.service.ai;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.retry.RetryUtils;
import org.springframework.ai.model.function.FunctionCallbackContext;
import org.springframework.core.io.ByteArrayResource;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.List;

/**
 * Base service for OpenAI-compatible AI providers.
 * Provides common functionality for OpenAI and OpenAI-compatible APIs like DeepSeek.
 */
public abstract class OpenAiCompatibleService implements AiProvider {
    
    private static final Logger logger = LoggerFactory.getLogger(OpenAiCompatibleService.class);
    
    protected final ChatModel chatModel;
    protected final String providerName;
    protected final String model;
    protected final boolean available;

    protected OpenAiCompatibleService(String providerName, String apiKey, String baseUrl, String model) {
        this.providerName = providerName;
        this.model = model;

        ChatModel tempChatModel = null;
        boolean tempAvailable = false;

        if (apiKey != null && !apiKey.trim().isEmpty()) {
            try {
                // Create OpenAI API client with custom base URL
                OpenAiApi openAiApi = OpenAiApi.builder()
                        .baseUrl(baseUrl)
                        .apiKey(apiKey)
                        .build();

                // Create chat model with default options
                OpenAiChatOptions defaultOptions = OpenAiChatOptions.builder()
                        .model(model)
                        .temperature(0.1)
                        .build();

                tempChatModel = new OpenAiChatModel(openAiApi, defaultOptions,
                        null, RetryUtils.DEFAULT_RETRY_TEMPLATE, null);
                tempAvailable = true;

                logger.info("✅ Initialized {} service with model: {} at {}", providerName, model, baseUrl);
            } catch (Exception e) {
                logger.error("❌ Failed to initialize {} client", providerName, e);
                tempChatModel = null;
                tempAvailable = false;
            }
        } else {
            logger.warn("❌ {} service not initialized - API key not configured", providerName);
        }

        this.chatModel = tempChatModel;
        this.available = tempAvailable;
    }
    
    @Override
    public String getProviderName() {
        return providerName;
    }
    
    @Override
    public boolean isAvailable() {
        return available && chatModel != null;
    }
    
    @Override
    public Mono<String> generateText(String prompt) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException(providerName + " service not available"));
        }
        
        return Mono.fromCallable(() -> {
            try {
                UserMessage userMessage = new UserMessage(prompt);
                ChatResponse response = chatModel.call(new Prompt(List.of(userMessage)));
                return response.getResult().getOutput().toString();
            } catch (Exception e) {
                logger.error("Error generating text with {}", providerName, e);
                throw new RuntimeException(providerName + " text generation failed", e);
            }
        })
        .subscribeOn(Schedulers.boundedElastic())
        .doOnSuccess(result -> logger.debug("{} text generation completed", providerName))
        .doOnError(error -> logger.error("{} text generation failed", providerName, error));
    }
    
    @Override
    public Mono<String> generateWithImage(String prompt, byte[] imageData, String mimeType) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException(providerName + " service not available"));
        }
        
        if (!supportsImageAnalysis()) {
            return Mono.error(new RuntimeException(providerName + " does not support image analysis"));
        }
        
        return Mono.fromCallable(() -> {
            try {
                logger.debug("Sending image analysis request to {} - Image size: {} bytes, MIME: {}",
                           providerName, imageData.length, mimeType);
                
                // Create image resource from byte array
                ByteArrayResource imageResource = new ByteArrayResource(imageData);
                
                // Create UserMessage with image and text
                UserMessage imageMessage = new UserMessage(imageResource);
                UserMessage textMessage = new UserMessage(prompt);
                
                // Create prompt with both image and text
                Prompt visionPrompt = new Prompt(List.of(imageMessage, textMessage));
                
                ChatResponse response = chatModel.call(visionPrompt);
                String result = response.getResult().getOutput().toString();
                
                logger.debug("{} image analysis completed - Response length: {} chars", providerName, result.length());
                return result;
            } catch (Exception e) {
                logger.error("Error generating content with image using {}", providerName, e);
                throw new RuntimeException(providerName + " image analysis failed", e);
            }
        })
        .subscribeOn(Schedulers.boundedElastic())
        .doOnSuccess(result -> logger.debug("{} image analysis completed successfully", providerName))
        .doOnError(error -> logger.error("{} image analysis failed", providerName, error));
    }
    
    @Override
    public Mono<String> generateWithSystemPrompt(String systemPrompt, String userPrompt) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException(providerName + " service not available"));
        }
        
        return Mono.fromCallable(() -> {
            try {
                // Use Spring AI's proper message system
                List<org.springframework.ai.chat.messages.Message> messages = List.of(
                    new SystemMessage(systemPrompt),
                    new UserMessage(userPrompt)
                );
                
                ChatResponse response = chatModel.call(new Prompt(messages));
                return response.getResult().getOutput().toString();
            } catch (Exception e) {
                logger.error("Error generating content with system prompt using {}", providerName, e);
                throw new RuntimeException(providerName + " generation with system prompt failed", e);
            }
        })
        .subscribeOn(Schedulers.boundedElastic())
        .doOnSuccess(result -> logger.debug("{} generation with system prompt completed", providerName))
        .doOnError(error -> logger.error("{} generation with system prompt failed", providerName, error));
    }
    
    @Override
    public Mono<String> generateWithImageAndSystemPrompt(String systemPrompt, String userPrompt, 
                                                         byte[] imageData, String mimeType) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException(providerName + " service not available"));
        }
        
        if (!supportsImageAnalysis()) {
            return Mono.error(new RuntimeException(providerName + " does not support image analysis"));
        }
        
        return Mono.fromCallable(() -> {
            try {
                logger.debug("Sending image analysis request to {} with system prompt - Image size: {} bytes, MIME: {}",
                           providerName, imageData.length, mimeType);
                
                // Create image resource from byte array
                ByteArrayResource imageResource = new ByteArrayResource(imageData);
                
                // Create messages with system prompt, image, and user prompt
                List<org.springframework.ai.chat.messages.Message> messages = List.of(
                    new SystemMessage(systemPrompt),
                    new UserMessage(imageResource),
                    new UserMessage(userPrompt)
                );
                
                Prompt visionPrompt = new Prompt(messages);
                ChatResponse response = chatModel.call(visionPrompt);
                String result = response.getResult().getOutput().toString();
                
                logger.debug("{} image analysis with system prompt completed - Response length: {} chars", 
                           providerName, result.length());
                return result;
            } catch (Exception e) {
                logger.error("Error generating content with image and system prompt using {}", providerName, e);
                throw new RuntimeException(providerName + " image analysis with system prompt failed", e);
            }
        })
        .subscribeOn(Schedulers.boundedElastic())
        .doOnSuccess(result -> logger.debug("{} image analysis with system prompt completed successfully", providerName))
        .doOnError(error -> logger.error("{} image analysis with system prompt failed", providerName, error));
    }
    
    @Override
    public boolean supportsImageAnalysis() {
        // Most OpenAI-compatible models support vision, but can be overridden by subclasses
        return true;
    }
    
    @Override
    public AiProviderCapabilities getCapabilities() {
        return AiProviderCapabilities.builder()
                .supportsText(true)
                .supportsImage(supportsImageAnalysis())
                .supportsSystemPrompts(true)
                .supportsStreaming(false) // Can be enabled in subclasses if needed
                .supportsFunctionCalling(false) // Can be enabled in subclasses if needed
                .build();
    }
}
