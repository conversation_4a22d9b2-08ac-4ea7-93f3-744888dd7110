package com.cbs.nepsetms.service.ai;

import com.cbs.nepsetms.config.ai.AiConfigurationProperties;
import com.google.genai.Client;
import com.google.genai.types.Blob;
import com.google.genai.types.Content;
import com.google.genai.types.GenerateContentConfig;
import com.google.genai.types.GenerateContentResponse;
import com.google.genai.types.Part;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Base64;

/**
 * Service for Google Generative AI (Gemini) integration.
 */
@Service
public class GeminiService implements AiProvider {
    
    private static final Logger logger = LoggerFactory.getLogger(GeminiService.class);
    
    private final Client client;
    private final AiConfigurationProperties.GeminiConfig config;

    public GeminiService(AiConfigurationProperties aiConfig) {
        logger.debug("Initializing GeminiService with aiConfig: {}", aiConfig);

        this.config = aiConfig != null ? aiConfig.gemini() : null;

        logger.debug("Extracted Gemini config: {}", config);

        if (config != null) {
            logger.debug("Gemini config found - API key present: {}", config.hasApiKey());
            if (config.hasApiKey()) {
                logger.debug("API key configured successfully");
            }
        } else {
            logger.warn("Gemini config is null - check configuration properties");
        }

        if (config != null && config.isAvailable()) {
            Client tempClient = null;
            try {
                tempClient = Client.builder()
                        .apiKey(config.apiKey())
                        .build();
                logger.info("✅ Initialized Gemini service with model: {}", config.model());
            } catch (Exception e) {
                logger.error("❌ Failed to initialize Gemini client", e);
            }
            this.client = tempClient;
        } else {
            this.client = null;
            logger.warn("❌ Gemini service not initialized - API key not configured");
        }
    }
    
    @Override
    public String getProviderName() {
        return "gemini";
    }
    
    /**
     * Check if Gemini is available.
     */
    public boolean isAvailable() {
        return client != null && config != null && config.isAvailable();
    }
    
    /**
     * Generate text content.
     */
    public Mono<String> generateText(String prompt) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException("Gemini service not available"));
        }

        return Mono.fromCallable(() -> {
            try {
                GenerateContentResponse response = client.models.generateContent(
                        config.model(),
                        prompt,
                        null
                );
                return response.text();
            } catch (Exception e) {
                logger.error("Error generating text with Gemini", e);
                throw new RuntimeException("Gemini text generation failed", e);
            }
        })
        .doOnSuccess(result -> logger.debug("Gemini text generation completed"))
        .doOnError(error -> logger.error("Gemini text generation failed", error));
    }
    
    /**
     * Generate content with image input using base64-encoded image data.
     */
    public Mono<String> generateWithImage(String prompt, byte[] imageData, String mimeType) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException("Gemini service not available"));
        }

        return Mono.fromCallable(() -> {
            try {
                // Create Blob from raw image data (the SDK will handle base64 encoding internally)
                Blob imageBlob = Blob.builder()
                    .mimeType(mimeType)
                    .data(imageData)
                    .build();

                // Create content with text and image parts
                Part imagePart = Part.builder()
                    .inlineData(imageBlob)
                    .build();

                Content content = Content.fromParts(
                    Part.fromText(prompt),
                    imagePart
                );

                logger.debug("Sending image analysis request to Gemini - Image size: {} bytes, MIME: {}",
                           imageData.length, mimeType);

                GenerateContentResponse response = client.models.generateContent(
                    config.model(),
                    content,
                    null
                );

                String result = response.text();
                logger.debug("Gemini image analysis completed - Response length: {} chars", result.length());
                return result;

            } catch (Exception e) {
                logger.error("Error generating content with image using Gemini", e);
                throw new RuntimeException("Gemini image analysis failed", e);
            }
        })
        .doOnSuccess(result -> logger.debug("Gemini image analysis completed successfully"))
        .doOnError(error -> logger.error("Gemini image analysis failed", error));
    }
    
    /**
     * Generate content with system prompt.
     */
    public Mono<String> generateWithSystemPrompt(String systemPrompt, String userPrompt) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException("Gemini service not available"));
        }

        // Combine system and user prompts since Gemini doesn't have explicit system messages
        String combinedPrompt = systemPrompt + "\n\nUser: " + userPrompt;
        return generateText(combinedPrompt);
    }

    /**
     * Generate content with image and system prompt using base64-encoded image data.
     */
    public Mono<String> generateWithImageAndSystemPrompt(String systemPrompt, String userPrompt,
                                                         byte[] imageData, String mimeType) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException("Gemini service not available"));
        }

        return Mono.fromCallable(() -> {
            try {
                // Create Blob from raw image data (the SDK will handle base64 encoding internally)
                Blob imageBlob = Blob.builder()
                    .mimeType(mimeType)
                    .data(imageData)
                    .build();

                // Create content with text and image parts
                Part imagePart = Part.builder()
                    .inlineData(imageBlob)
                    .build();

                Content content = Content.fromParts(
                    Part.fromText(userPrompt),
                    imagePart
                );

                // Create system instruction content
                Content systemInstruction = Content.fromParts(Part.fromText(systemPrompt));

                // Create configuration with system instruction
                GenerateContentConfig config = GenerateContentConfig.builder()
                    .systemInstruction(systemInstruction)
                    .build();

                logger.debug("Sending image analysis request to Gemini with system prompt - Image size: {} bytes, MIME: {}",
                           imageData.length, mimeType);

                GenerateContentResponse response = client.models.generateContent(
                    this.config.model(),
                    content,
                    config
                );

                String result = response.text();
                logger.debug("Gemini image analysis with system prompt completed - Response length: {} chars", result.length());
                return result;

            } catch (Exception e) {
                logger.error("Error generating content with image and system prompt using Gemini", e);
                throw new RuntimeException("Gemini image analysis with system prompt failed", e);
            }
        })
        .doOnSuccess(result -> logger.debug("Gemini image analysis with system prompt completed successfully"))
        .doOnError(error -> logger.error("Gemini image analysis with system prompt failed", error));
    }
    
    @Override
    public AiProviderCapabilities getCapabilities() {
        return AiProviderCapabilities.builder()
                .supportsText(true)
                .supportsImage(true)
                .supportsSystemPrompts(true)
                .supportsStreaming(false)
                .supportsFunctionCalling(false)
                .build();
    }
}
