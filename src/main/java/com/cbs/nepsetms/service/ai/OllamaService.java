package com.cbs.nepsetms.service.ai;

import com.cbs.nepsetms.config.ai.AiConfigurationProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.ollama.OllamaChatModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.ConvolveOp;
import java.awt.image.Kernel;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * Service for Ollama AI integration using Spring AI.
 */
@Service
@ConditionalOnProperty(prefix = "spring.ai.ollama.chat", name = "enabled", havingValue = "true", matchIfMissing = false)
public class OllamaService implements AiProvider {
    
    private static final Logger logger = LoggerFactory.getLogger(OllamaService.class);
    
    private final ChatModel chatModel;
    
    @Autowired
    public OllamaService(@Autowired(required = false) OllamaChatModel ollamaChatModel) {
        this.chatModel = ollamaChatModel;
        
        if (ollamaChatModel != null) {
            logger.info("✅ Initialized Ollama service with Spring AI - Using LLaVA 7B vision model");
        } else {
            logger.warn("❌ Ollama service not initialized - OllamaChatModel bean not available");
        }
    }
    
    @Override
    public String getProviderName() {
        return "ollama";
    }
    
    @Override
    public boolean isAvailable() {
        return chatModel != null;
    }
    
    @Override
    public Mono<String> generateText(String prompt) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException("Ollama service not available"));
        }
        
        return Mono.fromCallable(() -> {
            try {
                UserMessage userMessage = new UserMessage(prompt);
                ChatResponse response = chatModel.call(new Prompt(List.of(userMessage)));
                return response.getResult().getOutput().toString();
            } catch (Exception e) {
                logger.error("Error generating text with Ollama", e);
                throw new RuntimeException("Ollama text generation failed", e);
            }
        })
        .subscribeOn(Schedulers.boundedElastic())  // Execute on blocking thread pool
        .doOnSuccess(result -> logger.debug("Ollama text generation completed"))
        .doOnError(error -> logger.error("Ollama text generation failed", error));
    }
    
    @Override
    public Mono<String> generateWithImage(String prompt, byte[] imageData, String mimeType) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException("Ollama service not available"));
        }
        
        if (!supportsImageAnalysis()) {
            return Mono.error(new RuntimeException("Ollama model does not support image analysis"));
        }
        
        return Mono.fromCallable(() -> {
            try {
                logger.info("🔬 Simple Ollama image test - Image: {} bytes, Prompt: {}", imageData.length, prompt);
                
                // Create image resource from byte array
                ByteArrayResource imageResource = new ByteArrayResource(imageData);
                
                // Create UserMessage with image (Spring AI 1.0.0 supports this)
                UserMessage imageMessage = new UserMessage(imageResource);
                UserMessage textMessage = new UserMessage(prompt);
                
                // Create prompt with both image and text
                Prompt visionPrompt = new Prompt(List.of(imageMessage, textMessage));
                
                ChatResponse response = chatModel.call(visionPrompt);
                String result = response.getResult().getOutput().toString();
                
                logger.info("🎯 Simple image response: {}", result);
                return result;
            } catch (Exception e) {
                logger.error("❌ Simple image generation failed", e);
                throw new RuntimeException("Ollama image generation failed", e);
            }
        })
        .subscribeOn(Schedulers.boundedElastic())  // Execute on blocking thread pool
        .doOnSuccess(result -> logger.info("✅ Simple image generation completed"))
        .doOnError(error -> logger.error("❌ Simple image generation failed", error));
    }
    
    @Override
    public Mono<String> generateWithSystemPrompt(String systemPrompt, String userPrompt) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException("Ollama service not available"));
        }
        
        return Mono.fromCallable(() -> {
            try {
                // Use Spring AI's proper message system
                List<org.springframework.ai.chat.messages.Message> messages = List.of(
                    new SystemMessage(systemPrompt),
                    new UserMessage(userPrompt)
                );
                
                ChatResponse response = chatModel.call(new Prompt(messages));
                return response.getResult().getOutput().toString();
            } catch (Exception e) {
                logger.error("Error generating content with system prompt using Ollama", e);
                throw new RuntimeException("Ollama generation with system prompt failed", e);
            }
        })
        .subscribeOn(Schedulers.boundedElastic())  // Execute on blocking thread pool
        .doOnSuccess(result -> logger.debug("Ollama generation with system prompt completed"))
        .doOnError(error -> logger.error("Ollama generation with system prompt failed", error));
    }
    
    @Override
    public Mono<String> generateWithImageAndSystemPrompt(String systemPrompt, String userPrompt, 
                                                         byte[] imageData, String mimeType) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException("Ollama service not available"));
        }
        
        if (!supportsImageAnalysis()) {
            return Mono.error(new RuntimeException("Ollama model does not support image analysis"));
        }
        
        return Mono.fromCallable(() -> {
            try {
                logger.info("🔬 Testing Ollama image analysis - Image size: {} bytes, MIME: {}", imageData.length, mimeType);
                logger.info("📝 System prompt: {}", systemPrompt);
                logger.info("💬 User prompt: {}", userPrompt);
                
                // Strategy 1: Try UserMessage with ByteArrayResource
                ByteArrayResource imageResource = new ByteArrayResource(imageData);
                logger.info("📁 Created ByteArrayResource with {} bytes", imageResource.contentLength());
                
                // Try different message configurations
                List<org.springframework.ai.chat.messages.Message> messages;
                
                // Test 1: System + Image + Text
                messages = List.of(
                    new SystemMessage(systemPrompt),
                    new UserMessage(imageResource),
                    new UserMessage(userPrompt)
                );
                
                logger.info("🧪 Testing configuration: System + Image + Text ({} messages)", messages.size());
                
                Prompt visionPrompt = new Prompt(messages);
                ChatResponse response = chatModel.call(visionPrompt);
                String result = response.getResult().getOutput().toString();
                
                logger.info("🎯 Ollama response: {}", result);
                logger.info("✅ Response length: {} characters", result.length());
                
                return result;
            } catch (Exception e) {
                logger.error("❌ Ollama image analysis failed", e);
                
                // Try fallback approach
                logger.warn("🔄 Attempting fallback strategy...");
                try {
                    // Strategy 2: Just image without text
                    ByteArrayResource imageResource = new ByteArrayResource(imageData);
                    List<org.springframework.ai.chat.messages.Message> fallbackMessages = List.of(
                        new SystemMessage(systemPrompt + " " + userPrompt),
                        new UserMessage(imageResource)
                    );
                    
                    Prompt fallbackPrompt = new Prompt(fallbackMessages);
                    ChatResponse fallbackResponse = chatModel.call(fallbackPrompt);
                    String fallbackResult = fallbackResponse.getResult().getOutput().toString();
                    
                    logger.info("🔄 Fallback response: {}", fallbackResult);
                    return fallbackResult;
                } catch (Exception fallbackError) {
                    logger.error("❌ Fallback also failed", fallbackError);
                    throw new RuntimeException("All Ollama image strategies failed", fallbackError);
                }
            }
        })
        .subscribeOn(Schedulers.boundedElastic())
        .doOnSuccess(result -> logger.info("✅ Ollama image analysis completed"))
        .doOnError(error -> logger.error("❌ Ollama image analysis failed", error));
    }
    
    @Override
    public boolean supportsImageAnalysis() {
        // Testing Ollama/Gemma3 vision capabilities
        return chatModel != null;
    }
    
    
    @Override
    public AiProviderCapabilities getCapabilities() {
        return AiProviderCapabilities.builder()
                .supportsText(true)
                .supportsImage(true)  // Testing Ollama multimodal capabilities
                .supportsSystemPrompts(true)
                .supportsStreaming(true)
                .supportsFunctionCalling(false)
                .build();
    }
}