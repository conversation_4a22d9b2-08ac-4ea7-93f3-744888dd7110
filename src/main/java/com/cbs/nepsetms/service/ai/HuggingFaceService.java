package com.cbs.nepsetms.service.ai;

import com.cbs.nepsetms.config.ai.AiConfigurationProperties;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.Base64;
import java.util.List;
import java.util.Map;

/**
 * Service for Hugging Face AI integration with multiple vision language models.
 * Provides automatic fallback between different models for robust CAPTCHA solving.
 * 
 * Supported Models:
 * - LLaVA 1.5 (Primary) - Excellent for CAPTCHA and OCR tasks
 * - Idefics2 (Secondary) - Hugging Face's own multimodal model
 * - BLIP-2 (Tertiary) - Good for image captioning and analysis
 * - Phi-3 Vision (Quaternary) - Microsoft's compact vision model
 */
@Service
@ConditionalOnProperty(prefix = "nepse.ai.huggingface", name = "enabled", havingValue = "true")
public class HuggingFaceService implements AiProvider {
    
    private static final Logger logger = LoggerFactory.getLogger(HuggingFaceService.class);
    
    // Hugging Face API constants
    private static final String BASE_URL = "https://api-inference.huggingface.co/models/";
    private static final Duration REQUEST_TIMEOUT = Duration.ofSeconds(30);
    private static final int MAX_RETRIES = 2;
    
    // Vision Language Models in priority order (best to fallback)
    private static final List<VisionModel> VISION_MODELS = List.of(
        new VisionModel("llava-hf/llava-1.5-7b-hf", "LLaVA 1.5 7B", "Excellent for CAPTCHA and OCR tasks"),
        new VisionModel("HuggingFaceM4/idefics2-8b", "Idefics2 8B", "Hugging Face's own multimodal model"),
        new VisionModel("microsoft/Phi-3-vision-128k-instruct", "Phi-3 Vision", "Microsoft's compact vision model"),
        new VisionModel("Salesforce/blip2-opt-2.7b", "BLIP-2", "Good for image captioning and analysis")
    );
    
    private final WebClient webClient;
    private final ObjectMapper objectMapper;
    private final AiConfigurationProperties.HuggingFaceConfig config;
    private final boolean available;
    
    @Autowired
    public HuggingFaceService(WebClient.Builder webClientBuilder,
                             ObjectMapper objectMapper,
                             AiConfigurationProperties aiConfig) {
        this.objectMapper = objectMapper;
        this.config = aiConfig != null ? aiConfig.huggingface() : null;
        
        if (config != null && config.isAvailable()) {
            this.webClient = webClientBuilder
                .baseUrl(BASE_URL)
                .defaultHeader("Authorization", "Bearer " + config.apiKey())
                .defaultHeader("Content-Type", "application/json")
                .build();
            this.available = true;
            
            logger.info("✅ Hugging Face service initialized successfully");
            logger.info("📋 Available models: {}", VISION_MODELS.size());
            logger.info("🔧 Capabilities: Text generation, Image analysis, Multi-model fallback");
            logger.info("💰 Free tier: ~$0.10/month, PRO: $2.00/month");
            
            // Log available models
            VISION_MODELS.forEach(model -> 
                logger.debug("📱 Model: {} - {}", model.name(), model.description())
            );
        } else {
            this.webClient = null;
            this.available = false;
            
            if (config == null) {
                logger.warn("❌ Hugging Face service not available - no configuration found");
            } else if (!config.enabled()) {
                logger.info("ℹ️ Hugging Face service disabled via configuration");
            } else if (!config.hasApiKey()) {
                logger.warn("❌ Hugging Face service not available - API key not configured");
            }
        }
    }
    
    @Override
    public String getProviderName() {
        return "huggingface";
    }
    
    @Override
    public boolean isAvailable() {
        return available && webClient != null;
    }
    
    @Override
    public boolean supportsImageAnalysis() {
        return true; // Hugging Face has multiple vision models
    }
    
    @Override
    public Mono<String> generateText(String prompt) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException("Hugging Face service not available"));
        }
        
        // Use the first model for text-only generation (LLaVA can handle text-only)
        VisionModel model = VISION_MODELS.get(0);
        
        return generateWithModel(model.modelId(), prompt, null, null)
            .doOnSuccess(result -> logger.debug("Hugging Face text generation completed with {}", model.name()))
            .doOnError(error -> logger.error("Hugging Face text generation failed with {}", model.name(), error));
    }
    
    @Override
    public Mono<String> generateWithImage(String prompt, byte[] imageData, String mimeType) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException("Hugging Face service not available"));
        }
        
        if (!supportsImageAnalysis()) {
            return Mono.error(new RuntimeException("Hugging Face does not support image analysis"));
        }
        
        return generateWithImageFallback(prompt, imageData, mimeType, 0)
            .doOnSuccess(result -> logger.debug("Hugging Face image analysis completed successfully"))
            .doOnError(error -> logger.error("Hugging Face image analysis failed with all models", error));
    }
    
    /**
     * Recursive method to try different vision models with fallback
     */
    private Mono<String> generateWithImageFallback(String prompt, byte[] imageData, String mimeType, int modelIndex) {
        if (modelIndex >= VISION_MODELS.size()) {
            return Mono.error(new RuntimeException("All Hugging Face vision models failed"));
        }
        
        VisionModel model = VISION_MODELS.get(modelIndex);
        logger.debug("🎯 Attempting image analysis with Hugging Face model: {} (attempt {}/{})", 
                   model.name(), modelIndex + 1, VISION_MODELS.size());
        
        return generateWithModel(model.modelId(), prompt, imageData, mimeType)
            .doOnSuccess(result -> logger.info("✅ Hugging Face image analysis succeeded with {}", model.name()))
            .onErrorResume(error -> {
                logger.warn("❌ Hugging Face model {} failed: {}", model.name(), error.getMessage());
                
                // Try next model
                return generateWithImageFallback(prompt, imageData, mimeType, modelIndex + 1);
            });
    }
    
    /**
     * Generate content with a specific Hugging Face model
     */
    private Mono<String> generateWithModel(String modelId, String prompt, byte[] imageData, String mimeType) {
        return Mono.fromCallable(() -> {
            try {
                ObjectNode requestBody = objectMapper.createObjectNode();
                
                if (imageData != null) {
                    // For vision models, create multimodal input
                    ArrayNode inputs = objectMapper.createArrayNode();
                    
                    // Add image
                    ObjectNode imageInput = objectMapper.createObjectNode();
                    imageInput.put("type", "image");
                    String base64Image = Base64.getEncoder().encodeToString(imageData);
                    imageInput.put("image", "data:" + mimeType + ";base64," + base64Image);
                    inputs.add(imageInput);
                    
                    // Add text
                    ObjectNode textInput = objectMapper.createObjectNode();
                    textInput.put("type", "text");
                    textInput.put("text", prompt);
                    inputs.add(textInput);
                    
                    requestBody.set("inputs", inputs);
                } else {
                    // Text-only input
                    requestBody.put("inputs", prompt);
                }
                
                // Add parameters for better CAPTCHA solving
                ObjectNode parameters = objectMapper.createObjectNode();
                parameters.put("max_new_tokens", 100);
                parameters.put("temperature", 0.1); // Low temperature for consistent CAPTCHA solving
                parameters.put("do_sample", false);
                requestBody.set("parameters", parameters);
                
                logger.debug("Sending request to Hugging Face model: {} - Image: {} bytes", 
                           modelId, imageData != null ? imageData.length : 0);
                
                return requestBody;
            } catch (Exception e) {
                throw new RuntimeException("Failed to create Hugging Face request", e);
            }
        })
        .subscribeOn(Schedulers.boundedElastic())
        .flatMap(requestBody -> 
            webClient.post()
                .uri(modelId)
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(String.class)
                .timeout(REQUEST_TIMEOUT)
                .retryWhen(Retry.backoff(MAX_RETRIES, Duration.ofSeconds(1))
                    .filter(throwable -> !(throwable instanceof WebClientResponseException.BadRequest)))
        )
        .map(this::parseHuggingFaceResponse)
        .doOnError(error -> logger.error("Error calling Hugging Face model {}", modelId, error));
    }
    
    /**
     * Parse Hugging Face API response
     */
    private String parseHuggingFaceResponse(String response) {
        try {
            JsonNode jsonResponse = objectMapper.readTree(response);
            
            if (jsonResponse.isArray() && jsonResponse.size() > 0) {
                JsonNode firstResult = jsonResponse.get(0);
                
                // Handle different response formats
                if (firstResult.has("generated_text")) {
                    return firstResult.get("generated_text").asText();
                } else if (firstResult.has("text")) {
                    return firstResult.get("text").asText();
                } else if (firstResult.has("caption")) {
                    return firstResult.get("caption").asText();
                }
            } else if (jsonResponse.has("generated_text")) {
                return jsonResponse.get("generated_text").asText();
            } else if (jsonResponse.has("text")) {
                return jsonResponse.get("text").asText();
            }
            
            // Fallback: return the raw response
            return response;
        } catch (Exception e) {
            logger.warn("Failed to parse Hugging Face response, returning raw: {}", e.getMessage());
            return response;
        }
    }
    
    @Override
    public Mono<String> generateWithSystemPrompt(String systemPrompt, String userPrompt) {
        // Combine system and user prompts for models that don't support system prompts natively
        String combinedPrompt = systemPrompt + "\n\nUser: " + userPrompt + "\nAssistant:";
        return generateText(combinedPrompt);
    }
    
    @Override
    public Mono<String> generateWithImageAndSystemPrompt(String systemPrompt, String userPrompt, 
                                                         byte[] imageData, String mimeType) {
        // Combine system and user prompts for vision models
        String combinedPrompt = systemPrompt + "\n\nUser: " + userPrompt + "\nAssistant:";
        return generateWithImage(combinedPrompt, imageData, mimeType);
    }
    
    @Override
    public AiProviderCapabilities getCapabilities() {
        return AiProviderCapabilities.builder()
                .supportsText(true)
                .supportsImage(true)
                .supportsSystemPrompts(true) // Via prompt engineering
                .supportsStreaming(false) // Not implemented yet
                .supportsFunctionCalling(false) // Not supported by most vision models
                .build();
    }
    
    /**
     * Get information about available models
     */
    public List<VisionModel> getAvailableModels() {
        return VISION_MODELS;
    }
    
    /**
     * Get configuration details for debugging
     */
    public String getConfigInfo() {
        if (config == null) {
            return "No Hugging Face configuration found";
        }
        
        return String.format("Hugging Face Config - Enabled: %s, API Key: %s, Models: %d",
                config.enabled() ? "✓ Yes" : "✗ No",
                config.hasApiKey() ? "✓ Configured" : "✗ Missing",
                VISION_MODELS.size()
        );
    }
    
    /**
     * Record class for vision model information
     */
    public record VisionModel(String modelId, String name, String description) {}
}
