package com.cbs.nepsetms.service.ai;

import com.cbs.nepsetms.config.ai.AiConfigurationProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * AI service manager supporting multiple AI providers.
 * Provides unified interface for AI operations with provider selection.
 */
@Service
public class AiServiceManager {

    private static final Logger logger = LoggerFactory.getLogger(AiServiceManager.class);

    private final AiConfigurationProperties aiConfig;
    private final Map<String, AiProvider> providers = new ConcurrentHashMap<>();

    @Autowired(required = false)
    private GeminiService geminiService;

    @Autowired(required = false)
    private OllamaService ollamaService;

    @Autowired(required = false)
    private DeepSeekService deepSeekService;

    @Autowired(required = false)
    private OpenAiService openAiService;

    public AiServiceManager(AiConfigurationProperties aiConfig) {
        this.aiConfig = aiConfig;
        logger.info("Initialized AI Service Manager for multiple providers");
    }

    /**
     * Initialize providers after all beans are created.
     */
    @jakarta.annotation.PostConstruct
    public void initProviders() {
        if (geminiService != null && geminiService.isAvailable()) {
            providers.put("gemini", geminiService);
            logger.info("✅ Registered Gemini AI provider");
        }

        if (ollamaService != null && ollamaService.isAvailable()) {
            providers.put("ollama", ollamaService);
            logger.info("✅ Registered Ollama AI provider");
        }

        if (deepSeekService != null && deepSeekService.isAvailable()) {
            providers.put("deepseek", deepSeekService);
            logger.info("✅ Registered DeepSeek AI provider");
        }

        if (openAiService != null && openAiService.isAvailable()) {
            providers.put("openai", openAiService);
            logger.info("✅ Registered OpenAI provider");
        }

        logger.info("Available AI providers: {}", providers.keySet());
    }
    
    /**
     * Get the default or specified AI provider.
     */
    private AiProvider getProvider(String providerName) {
        String provider = providerName != null ? providerName : 
                         (aiConfig.defaultProvider() != null ? aiConfig.defaultProvider() : "gemini");
        
        AiProvider aiProvider = providers.get(provider);
        if (aiProvider == null) {
            throw new RuntimeException("AI provider '" + provider + "' not available. Available: " + providers.keySet());
        }
        return aiProvider;
    }

    /**
     * Generate text content using default provider.
     */
    public Mono<String> generateText(String prompt) {
        return generateText(prompt, null);
    }

    /**
     * Generate text content using specified provider.
     */
    public Mono<String> generateText(String prompt, String providerName) {
        try {
            AiProvider provider = getProvider(providerName);
            return provider.generateText(prompt)
                    .doOnSuccess(result -> logger.debug("{} text generation completed", provider.getProviderName()))
                    .doOnError(error -> logger.error("{} text generation failed", provider.getProviderName(), error));
        } catch (Exception e) {
            return Mono.error(e);
        }
    }

    /**
     * Generate content with image input using default provider.
     */
    public Mono<String> generateWithImage(String prompt, byte[] imageData) {
        return generateWithImage(prompt, imageData, null);
    }

    /**
     * Generate content with image input using specified provider.
     */
    public Mono<String> generateWithImage(String prompt, byte[] imageData, String providerName) {
        try {
            AiProvider provider = getProvider(providerName);
            if (!provider.supportsImageAnalysis()) {
                return Mono.error(new RuntimeException("Provider '" + provider.getProviderName() + "' does not support image analysis"));
            }
            return provider.generateWithImage(prompt, imageData, "image/png")
                    .doOnSuccess(result -> logger.debug("{} image generation completed", provider.getProviderName()))
                    .doOnError(error -> logger.error("{} image generation failed", provider.getProviderName(), error));
        } catch (Exception e) {
            return Mono.error(e);
        }
    }
    
    /**
     * Solve CAPTCHA using default or specified provider with configured system prompt.
     */
    public Mono<String> solveCaptcha(byte[] captchaImage) {
        return solveCaptcha(captchaImage, null);
    }

    /**
     * Solve CAPTCHA using specified provider with configured system prompt.
     */
    public Mono<String> solveCaptcha(byte[] captchaImage, String providerName) {
        try {
            AiProvider provider = getProvider(providerName);
            if (!provider.supportsImageAnalysis()) {
                return Mono.error(new RuntimeException("Provider '" + provider.getProviderName() + "' does not support image analysis for CAPTCHA solving"));
            }

            var captchaConfig = aiConfig.captchaSolving();
            String systemPrompt = captchaConfig != null ? captchaConfig.systemPrompt() :
                    "You are an expert at solving CAPTCHAs. Analyze the image and return only the text you see, nothing else.";

            return provider.generateWithImageAndSystemPrompt(
                    systemPrompt,
                    "What text do you see in this CAPTCHA image?",
                    captchaImage,
                    "image/png"
            )
            .map(String::trim)
            .doOnSuccess(solution -> logger.info("CAPTCHA solved successfully using {}: {}", provider.getProviderName(), solution))
            .doOnError(error -> logger.error("CAPTCHA solving failed with {}", provider.getProviderName(), error));
        } catch (Exception e) {
            return Mono.error(e);
        }
    }

    /**
     * Analyze an image using default provider.
     */
    public Mono<String> analyzeImage(byte[] imageData, String prompt) {
        return analyzeImage(imageData, prompt, null);
    }

    /**
     * Analyze an image using specified provider.
     */
    public Mono<String> analyzeImage(byte[] imageData, String prompt, String providerName) {
        return generateWithImage(prompt, imageData, providerName)
                .doOnSuccess(result -> logger.debug("Image analysis completed"))
                .doOnError(error -> logger.error("Image analysis failed", error));
    }

    /**
     * Check if a specific provider is available.
     */
    public boolean isProviderAvailable(String providerName) {
        AiProvider provider = providers.get(providerName);
        return provider != null && provider.isAvailable();
    }

    /**
     * Check if Gemini is available.
     */
    public boolean isGeminiAvailable() {
        return isProviderAvailable("gemini");
    }

    /**
     * Check if Ollama is available.
     */
    public boolean isOllamaAvailable() {
        return isProviderAvailable("ollama");
    }

    /**
     * Check if DeepSeek is available.
     */
    public boolean isDeepSeekAvailable() {
        return isProviderAvailable("deepseek");
    }

    /**
     * Check if OpenAI is available.
     */
    public boolean isOpenAiAvailable() {
        return isProviderAvailable("openai");
    }

    /**
     * Get available provider names.
     */
    public java.util.List<String> getAvailableProviders() {
        return new java.util.ArrayList<>(providers.keySet());
    }

    /**
     * Get provider capabilities.
     */
    public Map<String, AiProviderCapabilities> getProviderCapabilities() {
        Map<String, AiProviderCapabilities> capabilities = new ConcurrentHashMap<>();
        providers.forEach((name, provider) -> capabilities.put(name, provider.getCapabilities()));
        return capabilities;
    }
}
