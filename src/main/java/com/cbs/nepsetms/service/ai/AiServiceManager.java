package com.cbs.nepsetms.service.ai;

import com.cbs.nepsetms.config.ai.AiConfigurationProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * AI service manager supporting multiple AI providers.
 * Provides unified interface for AI operations with provider selection.
 */
@Service
public class AiServiceManager {

    private static final Logger logger = LoggerFactory.getLogger(AiServiceManager.class);

    private final AiConfigurationProperties aiConfig;
    private final Map<String, AiProvider> providers = new ConcurrentHashMap<>();

    @Autowired(required = false)
    private GeminiService geminiService;

    @Autowired(required = false)
    private OllamaService ollamaService;

    @Autowired(required = false)
    private DeepSeekService deepSeekService;

    public AiServiceManager(AiConfigurationProperties aiConfig) {
        this.aiConfig = aiConfig;
        logger.info("Initialized AI Service Manager for multiple providers");
    }

    /**
     * Initialize providers after all beans are created.
     */
    @jakarta.annotation.PostConstruct
    public void initProviders() {
        if (geminiService != null && geminiService.isAvailable()) {
            providers.put("gemini", geminiService);
            logger.info("✅ Registered Gemini AI provider");
        }

        if (ollamaService != null && ollamaService.isAvailable()) {
            providers.put("ollama", ollamaService);
            logger.info("✅ Registered Ollama AI provider");
        }

        if (deepSeekService != null && deepSeekService.isAvailable()) {
            providers.put("deepseek", deepSeekService);
            logger.info("✅ Registered DeepSeek AI provider");
        }

        if (openAiService != null && openAiService.isAvailable()) {
            providers.put("openai", openAiService);
            logger.info("✅ Registered OpenAI provider");
        }

        logger.info("Available AI providers: {}", providers.keySet());
    }
    
    /**
     * Get the default or specified AI provider.
     */
    private AiProvider getProvider(String providerName) {
        String provider = providerName != null ? providerName : 
                         (aiConfig.defaultProvider() != null ? aiConfig.defaultProvider() : "gemini");
        
        AiProvider aiProvider = providers.get(provider);
        if (aiProvider == null) {
            throw new RuntimeException("AI provider '" + provider + "' not available. Available: " + providers.keySet());
        }
        return aiProvider;
    }

    /**
     * Generate text content using default provider.
     */
    public Mono<String> generateText(String prompt) {
        return generateText(prompt, null);
    }

    /**
     * Generate text content using specified provider.
     */
    public Mono<String> generateText(String prompt, String providerName) {
        try {
            AiProvider provider = getProvider(providerName);
            return provider.generateText(prompt)
                    .doOnSuccess(result -> logger.debug("{} text generation completed", provider.getProviderName()))
                    .doOnError(error -> logger.error("{} text generation failed", provider.getProviderName(), error));
        } catch (Exception e) {
            return Mono.error(e);
        }
    }

    /**
     * Generate content with image input using default provider.
     */
    public Mono<String> generateWithImage(String prompt, byte[] imageData) {
        return generateWithImage(prompt, imageData, null);
    }

    /**
     * Generate content with image input using specified provider.
     */
    public Mono<String> generateWithImage(String prompt, byte[] imageData, String providerName) {
        try {
            AiProvider provider = getProvider(providerName);
            if (!provider.supportsImageAnalysis()) {
                return Mono.error(new RuntimeException("Provider '" + provider.getProviderName() + "' does not support image analysis"));
            }
            return provider.generateWithImage(prompt, imageData, "image/png")
                    .doOnSuccess(result -> logger.debug("{} image generation completed", provider.getProviderName()))
                    .doOnError(error -> logger.error("{} image generation failed", provider.getProviderName(), error));
        } catch (Exception e) {
            return Mono.error(e);
        }
    }
    
    /**
     * Solve CAPTCHA using default or specified provider with configured system prompt.
     */
    public Mono<String> solveCaptcha(byte[] captchaImage) {
        return solveCaptcha(captchaImage, null);
    }

    /**
     * Solve CAPTCHA using specified provider with configured system prompt.
     */
    public Mono<String> solveCaptcha(byte[] captchaImage, String providerName) {
        try {
            AiProvider provider = getProvider(providerName);
            if (!provider.supportsImageAnalysis()) {
                return Mono.error(new RuntimeException("Provider '" + provider.getProviderName() + "' does not support image analysis for CAPTCHA solving"));
            }

            var captchaConfig = aiConfig.captchaSolving();
            String systemPrompt = captchaConfig != null ? captchaConfig.systemPrompt() :
                    "You are an expert at solving CAPTCHAs. Analyze the image and return only the text you see, nothing else.";

            return provider.generateWithImageAndSystemPrompt(
                    systemPrompt,
                    "What text do you see in this CAPTCHA image?",
                    captchaImage,
                    "image/png"
            )
            .map(String::trim)
            .doOnSuccess(solution -> logger.info("CAPTCHA solved successfully using {}: {}", provider.getProviderName(), solution))
            .doOnError(error -> logger.error("CAPTCHA solving failed with {}", provider.getProviderName(), error));
        } catch (Exception e) {
            return Mono.error(e);
        }
    }

    /**
     * Solve CAPTCHA with automatic fallback to other available providers.
     * Tries providers in order of preference with retry logic.
     */
    public Mono<String> solveCaptchaWithFallback(byte[] captchaImage) {
        var captchaConfig = aiConfig.captchaSolving();
        int maxRetries = captchaConfig != null ? captchaConfig.maxRetries() : 2;
        String preferredProvider = captchaConfig != null ? captchaConfig.preferredProvider() : null;

        return solveCaptchaWithFallback(captchaImage, preferredProvider, maxRetries);
    }

    /**
     * Solve CAPTCHA with automatic fallback, specifying preferred provider and retry attempts.
     */
    public Mono<String> solveCaptchaWithFallback(byte[] captchaImage, String preferredProvider, int maxRetries) {
        List<String> availableProviders = getProvidersWithImageAnalysis(preferredProvider);

        if (availableProviders.isEmpty()) {
            return Mono.error(new RuntimeException("No AI providers available with image analysis capability for CAPTCHA solving"));
        }

        logger.info("🔄 Starting CAPTCHA solving with fallback. Available providers: {}", availableProviders);

        return tryProvidersSequentially(captchaImage, availableProviders, maxRetries, 0);
    }

    /**
     * Try providers sequentially with retry logic.
     */
    private Mono<String> tryProvidersSequentially(byte[] captchaImage, List<String> providers, int maxRetries, int currentProviderIndex) {
        if (currentProviderIndex >= providers.size()) {
            return Mono.error(new RuntimeException("All available AI providers failed to solve CAPTCHA after retries"));
        }

        String currentProvider = providers.get(currentProviderIndex);
        logger.info("🎯 Attempting CAPTCHA solving with provider: {} (attempt {}/{})",
                   currentProvider, currentProviderIndex + 1, providers.size());

        return solveCaptchaWithRetry(captchaImage, currentProvider, maxRetries)
                .doOnSuccess(solution -> logger.info("✅ CAPTCHA solved successfully with {}: {}", currentProvider, solution))
                .onErrorResume(error -> {
                    logger.warn("❌ Provider {} failed to solve CAPTCHA: {}", currentProvider, error.getMessage());
                    // Try next provider
                    return tryProvidersSequentially(captchaImage, providers, maxRetries, currentProviderIndex + 1);
                });
    }

    /**
     * Solve CAPTCHA with retry logic for a specific provider.
     */
    private Mono<String> solveCaptchaWithRetry(byte[] captchaImage, String providerName, int maxRetries) {
        return solveCaptcha(captchaImage, providerName)
                .retryWhen(Retry.backoff(maxRetries, Duration.ofSeconds(1))
                        .maxBackoff(Duration.ofSeconds(5))
                        .doBeforeRetry(retrySignal ->
                            logger.warn("🔄 Retrying CAPTCHA solving with {} (attempt {}/{})",
                                       providerName, retrySignal.totalRetries() + 1, maxRetries + 1))
                        .filter(throwable -> !(throwable instanceof RuntimeException &&
                                              throwable.getMessage().contains("does not support image analysis")))
                );
    }

    /**
     * Get list of providers that support image analysis, ordered by preference.
     */
    private List<String> getProvidersWithImageAnalysis(String preferredProvider) {
        if (preferredProvider != null) {
            // Use specific preferred provider first, then fallback to recommended order
            List<String> orderedProviders = new java.util.ArrayList<>();
            if (isProviderAvailable(preferredProvider) &&
                providers.get(preferredProvider).supportsImageAnalysis()) {
                orderedProviders.add(preferredProvider);
            }

            // Add other providers in recommended order
            List<String> recommended = getRecommendedCaptchaProviderOrder();
            for (String provider : recommended) {
                if (!provider.equals(preferredProvider)) {
                    orderedProviders.add(provider);
                }
            }

            return orderedProviders;
        } else {
            // Use recommended provider order
            return getRecommendedCaptchaProviderOrder();
        }
    }

    /**
     * Analyze an image using default provider.
     */
    public Mono<String> analyzeImage(byte[] imageData, String prompt) {
        return analyzeImage(imageData, prompt, null);
    }

    /**
     * Analyze an image using specified provider.
     */
    public Mono<String> analyzeImage(byte[] imageData, String prompt, String providerName) {
        return generateWithImage(prompt, imageData, providerName)
                .doOnSuccess(result -> logger.debug("Image analysis completed"))
                .doOnError(error -> logger.error("Image analysis failed", error));
    }

    /**
     * Check if a specific provider is available.
     */
    public boolean isProviderAvailable(String providerName) {
        AiProvider provider = providers.get(providerName);
        return provider != null && provider.isAvailable();
    }

    /**
     * Check if Gemini is available.
     */
    public boolean isGeminiAvailable() {
        return isProviderAvailable("gemini");
    }

    /**
     * Check if Ollama is available.
     */
    public boolean isOllamaAvailable() {
        return isProviderAvailable("ollama");
    }

    /**
     * Check if DeepSeek is available.
     */
    public boolean isDeepSeekAvailable() {
        return isProviderAvailable("deepseek");
    }

    /**
     * Check if OpenAI is available.
     */
    public boolean isOpenAiAvailable() {
        return isProviderAvailable("openai");
    }

    /**
     * Get available provider names.
     */
    public java.util.List<String> getAvailableProviders() {
        return new java.util.ArrayList<>(providers.keySet());
    }

    /**
     * Get provider capabilities.
     */
    public Map<String, AiProviderCapabilities> getProviderCapabilities() {
        Map<String, AiProviderCapabilities> capabilities = new ConcurrentHashMap<>();
        providers.forEach((name, provider) -> capabilities.put(name, provider.getCapabilities()));
        return capabilities;
    }

    /**
     * Get list of providers that support image analysis.
     */
    public List<String> getImageAnalysisCapableProviders() {
        return providers.entrySet().stream()
                .filter(entry -> entry.getValue().isAvailable() && entry.getValue().supportsImageAnalysis())
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    /**
     * Get detailed status of all providers for CAPTCHA solving capability.
     */
    public Map<String, CaptchaProviderStatus> getCaptchaProviderStatus() {
        Map<String, CaptchaProviderStatus> status = new ConcurrentHashMap<>();

        providers.forEach((name, provider) -> {
            boolean available = provider.isAvailable();
            boolean supportsImage = provider.supportsImageAnalysis();
            boolean captchaReady = available && supportsImage;

            status.put(name, new CaptchaProviderStatus(
                name,
                available,
                supportsImage,
                captchaReady,
                provider.getCapabilities()
            ));
        });

        return status;
    }

    /**
     * Check if any provider is available for CAPTCHA solving.
     */
    public boolean isCaptchaSolvingAvailable() {
        return providers.values().stream()
                .anyMatch(provider -> provider.isAvailable() && provider.supportsImageAnalysis());
    }

    /**
     * Get the recommended provider order for CAPTCHA solving.
     */
    public List<String> getRecommendedCaptchaProviderOrder() {
        List<String> imageCapableProviders = getImageAnalysisCapableProviders();

        // Prioritize based on typical CAPTCHA solving performance
        // 1. Gemini (excellent vision capabilities)
        // 2. OpenAI (good vision capabilities)
        // 3. DeepSeek (good and cost-effective)
        // 4. Others

        List<String> priorityOrder = List.of("gemini", "openai", "deepseek");
        List<String> orderedProviders = new java.util.ArrayList<>();

        // Add providers in priority order
        for (String priority : priorityOrder) {
            if (imageCapableProviders.contains(priority)) {
                orderedProviders.add(priority);
                imageCapableProviders.remove(priority);
            }
        }

        // Add remaining providers
        orderedProviders.addAll(imageCapableProviders);

        return orderedProviders;
    }

    /**
     * CAPTCHA provider status record.
     */
    public record CaptchaProviderStatus(
            String name,
            boolean available,
            boolean supportsImageAnalysis,
            boolean captchaReady,
            AiProviderCapabilities capabilities
    ) {}
}
