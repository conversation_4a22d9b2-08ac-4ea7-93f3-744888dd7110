package com.cbs.nepsetms.service.ai;

import com.cbs.nepsetms.config.ai.AiConfigurationProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * Service for DeepSeek AI integration using OpenAI-compatible API.
 * DeepSeek provides powerful language models with OpenAI API compatibility.
 */
@Service
@ConditionalOnProperty(prefix = "nepse.ai.deepseek", name = "api-key")
public class DeepSeekService extends OpenAiCompatibleService {
    
    private static final Logger logger = LoggerFactory.getLogger(DeepSeekService.class);
    
    // DeepSeek API constants
    private static final String DEFAULT_BASE_URL = "https://api.deepseek.com/v1";
    private static final String DEFAULT_MODEL = "deepseek-chat"; // DeepSeek-V3
    private static final String REASONING_MODEL = "deepseek-reasoner"; // DeepSeek-R1
    
    private final AiConfigurationProperties.DeepSeekConfig config;

    public DeepSeekService(AiConfigurationProperties aiConfig) {
        super(
            "deepseek",
            extractApiKey(aiConfig),
            extractBaseUrl(aiConfig),
            extractModel(aiConfig)
        );
        
        this.config = aiConfig != null ? aiConfig.deepseek() : null;
        
        if (isAvailable()) {
            logger.info("✅ DeepSeek service initialized successfully");
            logger.info("📋 Using model: {} at {}", extractModel(aiConfig), extractBaseUrl(aiConfig));
            logger.info("🔧 Capabilities: Text generation, Image analysis, System prompts");
        } else {
            logger.warn("❌ DeepSeek service not available - check API key configuration");
        }
    }
    
    private static String extractApiKey(AiConfigurationProperties aiConfig) {
        return aiConfig != null && aiConfig.deepseek() != null ? aiConfig.deepseek().apiKey() : null;
    }
    
    private static String extractBaseUrl(AiConfigurationProperties aiConfig) {
        if (aiConfig != null && aiConfig.deepseek() != null && aiConfig.deepseek().baseUrl() != null) {
            return aiConfig.deepseek().baseUrl();
        }
        return DEFAULT_BASE_URL;
    }
    
    private static String extractModel(AiConfigurationProperties aiConfig) {
        if (aiConfig != null && aiConfig.deepseek() != null && aiConfig.deepseek().model() != null) {
            return aiConfig.deepseek().model();
        }
        return DEFAULT_MODEL;
    }
    
    /**
     * Check if DeepSeek reasoning model is configured.
     */
    public boolean isReasoningModel() {
        return REASONING_MODEL.equals(model);
    }
    
    /**
     * Get the current model being used.
     */
    public String getCurrentModel() {
        return model;
    }
    
    /**
     * Check if this is the chat model (DeepSeek-V3).
     */
    public boolean isChatModel() {
        return DEFAULT_MODEL.equals(model);
    }
    
    @Override
    public boolean supportsImageAnalysis() {
        // DeepSeek-V3 (chat model) supports vision capabilities
        // DeepSeek-R1 (reasoning model) may have different capabilities
        return isChatModel();
    }
    
    @Override
    public AiProviderCapabilities getCapabilities() {
        return AiProviderCapabilities.builder()
                .supportsText(true)
                .supportsImage(supportsImageAnalysis())
                .supportsSystemPrompts(true)
                .supportsStreaming(false) // DeepSeek supports streaming but not implemented yet
                .supportsFunctionCalling(false) // DeepSeek supports function calling but not implemented yet
                .build();
    }
    
    /**
     * Get DeepSeek-specific model information.
     */
    public String getModelInfo() {
        return switch (model) {
            case DEFAULT_MODEL -> "DeepSeek-V3 (Chat Model) - Advanced conversational AI with vision capabilities";
            case REASONING_MODEL -> "DeepSeek-R1 (Reasoning Model) - Advanced reasoning and problem-solving AI";
            default -> "DeepSeek Model: " + model;
        };
    }
    
    /**
     * Check if the service has a valid configuration.
     */
    public boolean hasValidConfig() {
        return config != null && config.hasApiKey();
    }
    
    /**
     * Get configuration details for debugging.
     */
    public String getConfigInfo() {
        if (config == null) {
            return "No DeepSeek configuration found";
        }
        
        return String.format("DeepSeek Config - API Key: %s, Model: %s, Base URL: %s",
                config.hasApiKey() ? "✓ Configured" : "✗ Missing",
                config.model() != null ? config.model() : "Default (" + DEFAULT_MODEL + ")",
                config.baseUrl() != null ? config.baseUrl() : "Default (" + DEFAULT_BASE_URL + ")"
        );
    }
}
