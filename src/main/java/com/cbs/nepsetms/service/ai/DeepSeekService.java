package com.cbs.nepsetms.service.ai;

import com.cbs.nepsetms.config.ai.AiConfigurationProperties;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.deepseek.DeepSeekChatModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.Base64;
import java.util.List;

/**
 * Service for DeepSeek AI integration using Spring AI official DeepSeek support.
 * DeepSeek provides powerful language models with vision capabilities.
 */
@Service
@ConditionalOnProperty(prefix = "nepse.ai.deepseek", name = "enabled", havingValue = "true")
public class DeepSeekService implements AiProvider {

    private static final Logger logger = LoggerFactory.getLogger(DeepSeekService.class);

    // DeepSeek model constants
    private static final String DEFAULT_MODEL = "deepseek-chat"; // DeepSeek-V3
    private static final String REASONING_MODEL = "deepseek-reasoner"; // DeepSeek-R1

    private final ChatModel chatModel;
    private final AiConfigurationProperties.DeepSeekConfig config;
    private final String currentModel;
    private final boolean available;

    @Autowired
    public DeepSeekService(@Autowired(required = false) DeepSeekChatModel deepSeekChatModel,
                          AiConfigurationProperties aiConfig) {
        this.config = aiConfig != null ? aiConfig.deepseek() : null;
        this.currentModel = config != null && config.model() != null ? config.model() : DEFAULT_MODEL;

        if (deepSeekChatModel != null && config != null && config.isAvailable()) {
            this.chatModel = deepSeekChatModel;
            this.available = true;
            logger.info("✅ DeepSeek service initialized successfully");
            logger.info("📋 Using model: {}", currentModel);
            logger.info("🔧 Capabilities: Text generation, System prompts (Note: Image analysis not supported in Spring AI)");
        } else {
            this.chatModel = null;
            this.available = false;
            if (config == null) {
                logger.warn("❌ DeepSeek service not available - no configuration found");
            } else if (!config.enabled()) {
                logger.info("ℹ️ DeepSeek service disabled via configuration");
            } else if (!config.hasApiKey()) {
                logger.warn("❌ DeepSeek service not available - API key not configured");
            } else {
                logger.warn("❌ DeepSeek service not available - DeepSeekChatModel bean not found");
            }
        }
    }

    @Override
    public String getProviderName() {
        return "deepseek";
    }

    @Override
    public boolean isAvailable() {
        return available && chatModel != null;
    }

    @Override
    public Mono<String> generateText(String prompt) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException("DeepSeek service not available"));
        }

        return Mono.fromCallable(() -> {
            try {
                UserMessage userMessage = new UserMessage(prompt);
                ChatResponse response = chatModel.call(new Prompt(List.of(userMessage)));
                return response.getResult().getOutput().toString();
            } catch (Exception e) {
                logger.error("Error generating text with DeepSeek", e);
                throw new RuntimeException("DeepSeek text generation failed", e);
            }
        })
        .subscribeOn(Schedulers.boundedElastic())
        .doOnSuccess(result -> logger.debug("DeepSeek text generation completed"))
        .doOnError(error -> logger.error("DeepSeek text generation failed", error));
    }

    @Override
    public Mono<String> generateWithImage(String prompt, byte[] imageData, String mimeType) {
        // DeepSeek does not support image analysis in Spring AI
        return Mono.error(new RuntimeException("DeepSeek does not support image analysis in Spring AI"));
    }
    
    @Override
    public Mono<String> generateWithSystemPrompt(String systemPrompt, String userPrompt) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException("DeepSeek service not available"));
        }

        return Mono.fromCallable(() -> {
            try {
                // Use Spring AI's proper message system
                List<org.springframework.ai.chat.messages.Message> messages = List.of(
                    new SystemMessage(systemPrompt),
                    new UserMessage(userPrompt)
                );

                ChatResponse response = chatModel.call(new Prompt(messages));
                return response.getResult().getOutput().toString();
            } catch (Exception e) {
                logger.error("Error generating content with system prompt using DeepSeek", e);
                throw new RuntimeException("DeepSeek generation with system prompt failed", e);
            }
        })
        .subscribeOn(Schedulers.boundedElastic())
        .doOnSuccess(result -> logger.debug("DeepSeek generation with system prompt completed"))
        .doOnError(error -> logger.error("DeepSeek generation with system prompt failed", error));
    }

    @Override
    public Mono<String> generateWithImageAndSystemPrompt(String systemPrompt, String userPrompt,
                                                         byte[] imageData, String mimeType) {
        // DeepSeek does not support image analysis in Spring AI
        return Mono.error(new RuntimeException("DeepSeek does not support image analysis in Spring AI"));
    }

    /**
     * Check if DeepSeek reasoning model is configured.
     */
    public boolean isReasoningModel() {
        return REASONING_MODEL.equals(currentModel);
    }

    /**
     * Get the current model being used.
     */
    public String getCurrentModel() {
        return currentModel;
    }

    /**
     * Check if this is the chat model (DeepSeek-V3).
     */
    public boolean isChatModel() {
        return DEFAULT_MODEL.equals(currentModel);
    }
    
    @Override
    public boolean supportsImageAnalysis() {
        // According to Spring AI documentation, DeepSeek only supports text multimodality
        // Image analysis is not supported in the current Spring AI DeepSeek integration
        return false;
    }

    @Override
    public AiProviderCapabilities getCapabilities() {
        return AiProviderCapabilities.builder()
                .supportsText(true)
                .supportsImage(false) // DeepSeek does not support image analysis in Spring AI
                .supportsSystemPrompts(true)
                .supportsStreaming(false) // DeepSeek supports streaming but not implemented yet
                .supportsFunctionCalling(false) // DeepSeek supports function calling but not implemented yet
                .build();
    }

    /**
     * Get DeepSeek-specific model information.
     */
    public String getModelInfo() {
        return switch (currentModel) {
            case DEFAULT_MODEL -> "DeepSeek-V3 (Chat Model) - Advanced conversational AI (text-only in Spring AI)";
            case REASONING_MODEL -> "DeepSeek-R1 (Reasoning Model) - Advanced reasoning and problem-solving AI";
            default -> "DeepSeek Model: " + currentModel;
        };
    }

    /**
     * Check if the service has a valid configuration.
     */
    public boolean hasValidConfig() {
        return config != null && config.isAvailable();
    }

    /**
     * Get configuration details for debugging.
     */
    public String getConfigInfo() {
        if (config == null) {
            return "No DeepSeek configuration found";
        }

        return String.format("DeepSeek Config - Enabled: %s, API Key: %s, Model: %s",
                config.enabled() ? "✓ Yes" : "✗ No",
                config.hasApiKey() ? "✓ Configured" : "✗ Missing",
                config.model() != null ? config.model() : "Default (" + DEFAULT_MODEL + ")"
        );
    }
}
