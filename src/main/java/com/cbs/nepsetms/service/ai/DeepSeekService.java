package com.cbs.nepsetms.service.ai;

import com.cbs.nepsetms.config.ai.AiConfigurationProperties;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.deepseek.DeepSeekChatModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.retry.Retry;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.ConvolveOp;
import java.awt.image.Kernel;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.Duration;
import java.util.Base64;
import java.util.List;

/**
 * Service for DeepSeek AI integration using Spring AI official DeepSeek support.
 * DeepSeek provides powerful language models with vision capabilities.
 *
 * Note: While Spring AI doesn't expose DeepSeek's vision capabilities,
 * we implement direct HTTP calls for image analysis using the DeepSeek API.
 */
@Service
@ConditionalOnProperty(prefix = "nepse.ai.deepseek", name = "enabled", havingValue = "true")
public class DeepSeekService implements AiProvider {

    private static final Logger logger = LoggerFactory.getLogger(DeepSeekService.class);

    // DeepSeek model constants
    private static final String DEFAULT_MODEL = "deepseek-chat"; // DeepSeek-V3
    private static final String REASONING_MODEL = "deepseek-reasoner"; // DeepSeek-R1

    // DeepSeek API constants for direct HTTP calls (vision support)
    private static final String DEEPSEEK_API_URL = "https://api.deepseek.com/chat/completions";
    private static final int MAX_RETRIES = 2;
    private static final Duration TIMEOUT = Duration.ofSeconds(30);

    private final ChatModel chatModel;
    private final AiConfigurationProperties.DeepSeekConfig config;
    private final AiConfigurationProperties.CaptchaSolvingConfig captchaConfig;
    private final String currentModel;
    private final boolean available;
    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    @Autowired
    public DeepSeekService(@Autowired(required = false) DeepSeekChatModel deepSeekChatModel,
                          AiConfigurationProperties aiConfig) {
        this.config = aiConfig != null ? aiConfig.deepseek() : null;
        this.captchaConfig = aiConfig != null ? aiConfig.captchaSolving() : null;
        this.currentModel = config != null && config.model() != null ? config.model() : DEFAULT_MODEL;
        this.objectMapper = new ObjectMapper();

        // Initialize WebClient for direct API calls (vision support)
        this.webClient = WebClient.builder()
            .baseUrl(DEEPSEEK_API_URL)
            .defaultHeader("Content-Type", "application/json")
            .build();

        if (deepSeekChatModel != null && config != null && config.isAvailable()) {
            this.chatModel = deepSeekChatModel;
            this.available = true;
            logger.info("✅ DeepSeek service initialized successfully");
            logger.info("📋 Using model: {}", currentModel);
            logger.info("🔧 Capabilities: Text generation, System prompts, Image analysis (via direct API)");
            logger.info("🎯 Vision support: Enabled via direct HTTP calls to DeepSeek API");
        } else {
            this.chatModel = null;
            this.available = false;
            if (config == null) {
                logger.warn("❌ DeepSeek service not available - no configuration found");
            } else if (!config.enabled()) {
                logger.info("ℹ️ DeepSeek service disabled via configuration");
            } else if (!config.hasApiKey()) {
                logger.warn("❌ DeepSeek service not available - API key not configured");
            } else {
                logger.warn("❌ DeepSeek service not available - DeepSeekChatModel bean not found");
            }
        }
    }

    @Override
    public String getProviderName() {
        return "deepseek";
    }

    @Override
    public boolean isAvailable() {
        return available && chatModel != null;
    }

    @Override
    public Mono<String> generateText(String prompt) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException("DeepSeek service not available"));
        }

        return Mono.fromCallable(() -> {
            try {
                UserMessage userMessage = new UserMessage(prompt);
                ChatResponse response = chatModel.call(new Prompt(List.of(userMessage)));
                return response.getResult().getOutput().toString();
            } catch (Exception e) {
                logger.error("Error generating text with DeepSeek", e);
                throw new RuntimeException("DeepSeek text generation failed", e);
            }
        })
        .subscribeOn(Schedulers.boundedElastic())
        .doOnSuccess(result -> logger.debug("DeepSeek text generation completed"))
        .doOnError(error -> logger.error("DeepSeek text generation failed", error));
    }

    @Override
    public Mono<String> generateWithImage(String prompt, byte[] imageData, String mimeType) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException("DeepSeek service not available"));
        }

        if (imageData == null || imageData.length == 0) {
            return generateText(prompt);
        }

        return generateVisionContentWithFallback(prompt, imageData, mimeType)
            .doOnSubscribe(s -> logger.debug("🎯 Starting DeepSeek vision analysis - Image: {} bytes", imageData.length))
            .doOnSuccess(result -> logger.debug("✅ DeepSeek vision analysis completed"))
            .doOnError(error -> logger.error("❌ DeepSeek vision analysis failed: {}", error.getMessage()));
    }
    
    @Override
    public Mono<String> generateWithSystemPrompt(String systemPrompt, String userPrompt) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException("DeepSeek service not available"));
        }

        return Mono.fromCallable(() -> {
            try {
                // Use Spring AI's proper message system
                List<org.springframework.ai.chat.messages.Message> messages = List.of(
                    new SystemMessage(systemPrompt),
                    new UserMessage(userPrompt)
                );

                ChatResponse response = chatModel.call(new Prompt(messages));
                return response.getResult().getOutput().toString();
            } catch (Exception e) {
                logger.error("Error generating content with system prompt using DeepSeek", e);
                throw new RuntimeException("DeepSeek generation with system prompt failed", e);
            }
        })
        .subscribeOn(Schedulers.boundedElastic())
        .doOnSuccess(result -> logger.debug("DeepSeek generation with system prompt completed"))
        .doOnError(error -> logger.error("DeepSeek generation with system prompt failed", error));
    }

    @Override
    public Mono<String> generateWithImageAndSystemPrompt(String systemPrompt, String userPrompt,
                                                         byte[] imageData, String mimeType) {
        if (!isAvailable()) {
            return Mono.error(new RuntimeException("DeepSeek service not available"));
        }

        if (imageData == null || imageData.length == 0) {
            return generateWithSystemPrompt(systemPrompt, userPrompt);
        }

        return generateVisionContentWithSystemPromptAndFallback(systemPrompt, userPrompt, imageData, mimeType)
            .doOnSubscribe(s -> logger.debug("🎯 Starting DeepSeek vision analysis with system prompt - Image: {} bytes", imageData.length))
            .doOnSuccess(result -> logger.debug("✅ DeepSeek vision analysis with system prompt completed"))
            .doOnError(error -> logger.error("❌ DeepSeek vision analysis with system prompt failed: {}", error.getMessage()));
    }

    /**
     * Check if DeepSeek reasoning model is configured.
     */
    public boolean isReasoningModel() {
        return REASONING_MODEL.equals(currentModel);
    }

    /**
     * Get the current model being used.
     */
    public String getCurrentModel() {
        return currentModel;
    }

    /**
     * Check if this is the chat model (DeepSeek-V3).
     */
    public boolean isChatModel() {
        return DEFAULT_MODEL.equals(currentModel);
    }
    
    @Override
    public boolean supportsImageAnalysis() {
        // Both DeepSeek-chat and DeepSeek-reasoner models support image analysis via direct API calls
        // We discovered this through testing - both models can analyze base64 images
        // DeepSeek-reasoner might provide better accuracy for complex visual tasks like CAPTCHA
        return isAvailable() && (DEFAULT_MODEL.equals(currentModel) || REASONING_MODEL.equals(currentModel));
    }

    @Override
    public AiProviderCapabilities getCapabilities() {
        return AiProviderCapabilities.builder()
                .supportsText(true)
                .supportsImage(supportsImageAnalysis()) // DeepSeek-chat supports image analysis via direct API
                .supportsSystemPrompts(true)
                .supportsStreaming(false) // DeepSeek supports streaming but not implemented yet
                .supportsFunctionCalling(false) // DeepSeek supports function calling but not implemented yet
                .build();
    }

    /**
     * Get DeepSeek-specific model information.
     */
    public String getModelInfo() {
        return switch (currentModel) {
            case DEFAULT_MODEL -> "DeepSeek-V3 (Chat Model) - Advanced conversational AI with vision capabilities";
            case REASONING_MODEL -> "DeepSeek-R1 (Reasoning Model) - Advanced reasoning and problem-solving AI with vision capabilities (excellent for CAPTCHA)";
            default -> "DeepSeek Model: " + currentModel;
        };
    }

    /**
     * Check if the service has a valid configuration.
     */
    public boolean hasValidConfig() {
        return config != null && config.isAvailable();
    }

    /**
     * Get configuration details for debugging.
     */
    public String getConfigInfo() {
        if (config == null) {
            return "No DeepSeek configuration found";
        }

        return String.format("DeepSeek Config - Enabled: %s, API Key: %s, Model: %s, Vision: %s",
                config.enabled() ? "✓ Yes" : "✗ No",
                config.hasApiKey() ? "✓ Configured" : "✗ Missing",
                config.model() != null ? config.model() : "Default (" + DEFAULT_MODEL + ")",
                supportsImageAnalysis() ? "✓ Supported" : "✗ Not Available"
        );
    }

    /**
     * Generate content with image using direct DeepSeek API calls.
     * This bypasses Spring AI to access DeepSeek's vision capabilities.
     */
    private Mono<String> generateVisionContent(String prompt, byte[] imageData, String mimeType) {
        return Mono.fromCallable(() -> {
            try {
                ObjectNode requestBody = objectMapper.createObjectNode();
                requestBody.put("model", currentModel);

                // Create messages array
                ArrayNode messages = objectMapper.createArrayNode();

                // User message with image in base64 format (the format that works!)
                ObjectNode userMessage = objectMapper.createObjectNode();
                userMessage.put("role", "user");

                // Format: ![](data:image/png;base64,{base64}) prompt
                // Optimized for CAPTCHA solving with enhanced prompt
                String base64Image = Base64.getEncoder().encodeToString(imageData);
                String enhancedPrompt = enhanceCaptchaPrompt(prompt);
                String visionPrompt = String.format("![](data:%s;base64,%s) %s",
                                                   mimeType, base64Image, enhancedPrompt);

                // Debug logging for image data
                logger.debug("🔍 DeepSeek Vision Debug - Model: {}, Image size: {} bytes, MIME: {}",
                           currentModel, imageData.length, mimeType);
                logger.debug("🔍 Base64 prefix: {}...", base64Image.substring(0, Math.min(50, base64Image.length())));
                logger.debug("🔍 Enhanced prompt: {}", enhancedPrompt);

                userMessage.put("content", visionPrompt);
                messages.add(userMessage);

                requestBody.set("messages", messages);
                // Use CAPTCHA configuration for optimal settings
                requestBody.put("max_tokens", captchaConfig != null ? captchaConfig.maxTokens() : 50);
                requestBody.put("temperature", captchaConfig != null ? captchaConfig.temperature() : 0.0);

                return requestBody;
            } catch (Exception e) {
                throw new RuntimeException("Failed to create DeepSeek vision request", e);
            }
        })
        .flatMap(this::sendDeepSeekRequest)
        .retryWhen(Retry.backoff(MAX_RETRIES, Duration.ofSeconds(1))
            .filter(throwable -> throwable instanceof WebClientResponseException.TooManyRequests))
        .subscribeOn(Schedulers.boundedElastic());
    }

    /**
     * Generate content with image and system prompt using direct DeepSeek API calls.
     */
    private Mono<String> generateVisionContentWithSystemPrompt(String systemPrompt, String userPrompt,
                                                              byte[] imageData, String mimeType) {
        return Mono.fromCallable(() -> {
            try {
                ObjectNode requestBody = objectMapper.createObjectNode();
                requestBody.put("model", currentModel);

                // Create messages array
                ArrayNode messages = objectMapper.createArrayNode();

                // System message
                ObjectNode systemMessage = objectMapper.createObjectNode();
                systemMessage.put("role", "system");
                systemMessage.put("content", systemPrompt);
                messages.add(systemMessage);

                // User message with image
                ObjectNode userMessage = objectMapper.createObjectNode();
                userMessage.put("role", "user");

                // Format: ![](data:image/png;base64,{base64}) prompt
                // Optimized for CAPTCHA solving with enhanced prompt
                String base64Image = Base64.getEncoder().encodeToString(imageData);
                String enhancedPrompt = enhanceCaptchaPrompt(userPrompt);
                String visionPrompt = String.format("![](data:%s;base64,%s) %s",
                                                   mimeType, base64Image, enhancedPrompt);

                // Debug logging for image data
                logger.debug("🔍 DeepSeek Vision Debug (with system) - Model: {}, Image size: {} bytes, MIME: {}",
                           currentModel, imageData.length, mimeType);
                logger.debug("🔍 System prompt: {}", systemPrompt);
                logger.debug("🔍 Enhanced user prompt: {}", enhancedPrompt);

                userMessage.put("content", visionPrompt);
                messages.add(userMessage);

                requestBody.set("messages", messages);
                // Use CAPTCHA configuration for optimal settings
                requestBody.put("max_tokens", captchaConfig != null ? captchaConfig.maxTokens() : 50);
                requestBody.put("temperature", captchaConfig != null ? captchaConfig.temperature() : 0.0);

                return requestBody;
            } catch (Exception e) {
                throw new RuntimeException("Failed to create DeepSeek vision request with system prompt", e);
            }
        })
        .flatMap(this::sendDeepSeekRequest)
        .retryWhen(Retry.backoff(MAX_RETRIES, Duration.ofSeconds(1))
            .filter(throwable -> throwable instanceof WebClientResponseException.TooManyRequests))
        .subscribeOn(Schedulers.boundedElastic());
    }

    /**
     * Send request to DeepSeek API and parse response.
     */
    private Mono<String> sendDeepSeekRequest(ObjectNode requestBody) {
        if (!config.hasApiKey()) {
            return Mono.error(new RuntimeException("DeepSeek API key not configured"));
        }

        return webClient.post()
            .header("Authorization", "Bearer " + config.apiKey())
            .bodyValue(requestBody)
            .retrieve()
            .bodyToMono(String.class)
            .timeout(TIMEOUT)
            .map(this::parseDeepSeekResponse)
            .onErrorMap(WebClientResponseException.class, ex -> {
                String errorMsg = String.format("DeepSeek API error: %d %s", ex.getStatusCode().value(), ex.getStatusText());
                logger.error("❌ {}", errorMsg);
                return new RuntimeException(errorMsg, ex);
            });
    }

    /**
     * Parse DeepSeek API response to extract the generated content.
     */
    private String parseDeepSeekResponse(String response) {
        try {
            // Debug: Log the raw response (truncated for readability)
            String truncatedResponse = response.length() > 500 ? response.substring(0, 500) + "..." : response;
            logger.debug("🔍 DeepSeek raw response: {}", truncatedResponse);

            JsonNode jsonResponse = objectMapper.readTree(response);

            if (jsonResponse.has("choices") && jsonResponse.get("choices").isArray()) {
                JsonNode choices = jsonResponse.get("choices");
                if (choices.size() > 0) {
                    JsonNode firstChoice = choices.get(0);
                    if (firstChoice.has("message") && firstChoice.get("message").has("content")) {
                        String content = firstChoice.get("message").get("content").asText();
                        logger.debug("🔍 DeepSeek extracted content: '{}'", content);

                        // Clean up the response for CAPTCHA text
                        String cleaned = cleanCaptchaResponse(content);
                        logger.debug("🔍 DeepSeek cleaned response: '{}'", cleaned);
                        return cleaned;
                    }
                }
            }

            // If we can't parse the expected format, return the raw response
            logger.warn("❌ Unexpected DeepSeek response format, returning raw response");
            return response;
        } catch (Exception e) {
            logger.warn("❌ Failed to parse DeepSeek response: {}", e.getMessage());
            return response;
        }
    }

    /**
     * Enhance the prompt specifically for CAPTCHA solving with DeepSeek.
     * Optimized for the specific CAPTCHA format: 6 lowercase letters with gray speckled background.
     */
    private String enhanceCaptchaPrompt(String originalPrompt) {
        // If the prompt already contains detailed CAPTCHA instructions, use it as-is
        if (originalPrompt.toLowerCase().contains("captcha") || originalPrompt.toLowerCase().contains("ocr")) {
            return originalPrompt;
        }

        // Enhance with specific instructions for this CAPTCHA type
        return "Read the 6 lowercase letters from this CAPTCHA image. " +
               "The text is dark and located in the center. " +
               "Ignore the gray speckled background noise. " +
               "Return only the 6 letters: " + originalPrompt;
    }

    /**
     * Clean up the CAPTCHA response to extract only the relevant text.
     * DeepSeek sometimes adds explanations even when asked not to.
     */
    private String cleanCaptchaResponse(String response) {
        if (response == null || response.trim().isEmpty()) {
            return response;
        }

        String cleaned = response.trim();

        // Remove common prefixes that DeepSeek might add
        String[] prefixesToRemove = {
            "The text in the image is:",
            "The CAPTCHA text is:",
            "I can see:",
            "The characters are:",
            "The text shows:",
            "Looking at the image, I see:",
            "The alphanumeric text is:"
        };

        for (String prefix : prefixesToRemove) {
            if (cleaned.toLowerCase().startsWith(prefix.toLowerCase())) {
                cleaned = cleaned.substring(prefix.length()).trim();
                break;
            }
        }

        // Remove quotes if present
        if (cleaned.startsWith("\"") && cleaned.endsWith("\"")) {
            cleaned = cleaned.substring(1, cleaned.length() - 1);
        }
        if (cleaned.startsWith("'") && cleaned.endsWith("'")) {
            cleaned = cleaned.substring(1, cleaned.length() - 1);
        }

        // Extract only alphanumeric characters if the response contains extra text
        // Look for the first sequence of alphanumeric characters
        String alphanumericPattern = "[A-Za-z0-9]+";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(alphanumericPattern);
        java.util.regex.Matcher matcher = pattern.matcher(cleaned);

        if (matcher.find()) {
            String extracted = matcher.group();
            // If the extracted text is reasonable length for a CAPTCHA (3-8 characters), use it
            if (extracted.length() >= 3 && extracted.length() <= 8) {
                logger.debug("🧹 Cleaned CAPTCHA response: '{}' -> '{}'", response, extracted);
                return extracted;
            }
        }

        // If no good alphanumeric sequence found, return the cleaned response
        logger.debug("🧹 Cleaned CAPTCHA response: '{}' -> '{}'", response, cleaned);
        return cleaned;
    }

    /**
     * Generate vision content with fallback between models.
     * Try the configured model first, then fallback to the other model if it fails.
     */
    private Mono<String> generateVisionContentWithFallback(String prompt, byte[] imageData, String mimeType) {
        String primaryModel = currentModel;
        String fallbackModel = DEFAULT_MODEL.equals(currentModel) ? REASONING_MODEL : DEFAULT_MODEL;

        logger.debug("🔄 DeepSeek vision with fallback - Primary: {}, Fallback: {}", primaryModel, fallbackModel);

        return generateVisionContentWithModel(prompt, imageData, mimeType, primaryModel)
            .onErrorResume(error -> {
                logger.warn("⚠️ Primary model {} failed, trying fallback model {}: {}",
                          primaryModel, fallbackModel, error.getMessage());
                return generateVisionContentWithModel(prompt, imageData, mimeType, fallbackModel);
            });
    }

    /**
     * Generate vision content with system prompt and fallback between models.
     */
    private Mono<String> generateVisionContentWithSystemPromptAndFallback(String systemPrompt, String userPrompt,
                                                                          byte[] imageData, String mimeType) {
        String primaryModel = currentModel;
        String fallbackModel = DEFAULT_MODEL.equals(currentModel) ? REASONING_MODEL : DEFAULT_MODEL;

        logger.debug("🔄 DeepSeek vision with system prompt and fallback - Primary: {}, Fallback: {}",
                   primaryModel, fallbackModel);

        return generateVisionContentWithSystemPromptAndModel(systemPrompt, userPrompt, imageData, mimeType, primaryModel)
            .onErrorResume(error -> {
                logger.warn("⚠️ Primary model {} failed, trying fallback model {}: {}",
                          primaryModel, fallbackModel, error.getMessage());
                return generateVisionContentWithSystemPromptAndModel(systemPrompt, userPrompt, imageData, mimeType, fallbackModel);
            });
    }

    /**
     * Generate vision content with a specific model.
     */
    private Mono<String> generateVisionContentWithModel(String prompt, byte[] imageData, String mimeType, String model) {
        return Mono.fromCallable(() -> {
            try {
                ObjectNode requestBody = objectMapper.createObjectNode();
                requestBody.put("model", model);

                // Create messages array
                ArrayNode messages = objectMapper.createArrayNode();

                // User message with image in base64 format (the format that works!)
                ObjectNode userMessage = objectMapper.createObjectNode();
                userMessage.put("role", "user");

                // Format: ![](data:image/png;base64,{base64}) prompt
                // Optimized for CAPTCHA solving with enhanced prompt
                String base64Image = Base64.getEncoder().encodeToString(imageData);
                String enhancedPrompt = enhanceCaptchaPrompt(prompt);
                String visionPrompt = String.format("![](data:%s;base64,%s) %s",
                                                   mimeType, base64Image, enhancedPrompt);

                // Debug logging for image data
                logger.debug("🔍 DeepSeek Vision Debug - Model: {}, Image size: {} bytes, MIME: {}",
                           model, imageData.length, mimeType);
                logger.debug("🔍 Enhanced prompt: {}", enhancedPrompt);

                userMessage.put("content", visionPrompt);
                messages.add(userMessage);

                requestBody.set("messages", messages);
                // Use CAPTCHA configuration for optimal settings
                requestBody.put("max_tokens", captchaConfig != null ? captchaConfig.maxTokens() : 50);
                requestBody.put("temperature", captchaConfig != null ? captchaConfig.temperature() : 0.0);

                return requestBody;
            } catch (Exception e) {
                throw new RuntimeException("Failed to create DeepSeek vision request for model: " + model, e);
            }
        })
        .flatMap(this::sendDeepSeekRequest)
        .retryWhen(Retry.backoff(MAX_RETRIES, Duration.ofSeconds(1))
            .filter(throwable -> throwable instanceof WebClientResponseException.TooManyRequests))
        .subscribeOn(Schedulers.boundedElastic());
    }

    /**
     * Generate vision content with system prompt and a specific model.
     */
    private Mono<String> generateVisionContentWithSystemPromptAndModel(String systemPrompt, String userPrompt,
                                                                       byte[] imageData, String mimeType, String model) {
        return Mono.fromCallable(() -> {
            try {
                ObjectNode requestBody = objectMapper.createObjectNode();
                requestBody.put("model", model);

                // Create messages array
                ArrayNode messages = objectMapper.createArrayNode();

                // System message
                ObjectNode systemMessage = objectMapper.createObjectNode();
                systemMessage.put("role", "system");
                systemMessage.put("content", systemPrompt);
                messages.add(systemMessage);

                // User message with image
                ObjectNode userMessage = objectMapper.createObjectNode();
                userMessage.put("role", "user");

                // Format: ![](data:image/png;base64,{base64}) prompt
                // Optimized for CAPTCHA solving with enhanced prompt
                String base64Image = Base64.getEncoder().encodeToString(imageData);
                String enhancedPrompt = enhanceCaptchaPrompt(userPrompt);
                String visionPrompt = String.format("![](data:%s;base64,%s) %s",
                                                   mimeType, base64Image, enhancedPrompt);

                // Debug logging for image data
                logger.debug("🔍 DeepSeek Vision Debug (with system) - Model: {}, Image size: {} bytes, MIME: {}",
                           model, imageData.length, mimeType);
                logger.debug("🔍 System prompt: {}", systemPrompt);
                logger.debug("🔍 Enhanced user prompt: {}", enhancedPrompt);

                userMessage.put("content", visionPrompt);
                messages.add(userMessage);

                requestBody.set("messages", messages);
                // Use CAPTCHA configuration for optimal settings
                requestBody.put("max_tokens", captchaConfig != null ? captchaConfig.maxTokens() : 50);
                requestBody.put("temperature", captchaConfig != null ? captchaConfig.temperature() : 0.0);

                return requestBody;
            } catch (Exception e) {
                throw new RuntimeException("Failed to create DeepSeek vision request with system prompt for model: " + model, e);
            }
        })
        .flatMap(this::sendDeepSeekRequest)
        .retryWhen(Retry.backoff(MAX_RETRIES, Duration.ofSeconds(1))
            .filter(throwable -> throwable instanceof WebClientResponseException.TooManyRequests))
        .subscribeOn(Schedulers.boundedElastic());
    }
}
