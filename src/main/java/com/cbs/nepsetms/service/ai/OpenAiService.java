package com.cbs.nepsetms.service.ai;

import com.cbs.nepsetms.config.ai.AiConfigurationProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * Service for OpenAI integration using Spring AI.
 * Provides access to OpenAI's GPT models including GPT-4, GPT-4 Vision, etc.
 */
@Service
@ConditionalOnProperty(prefix = "nepse.ai.openai", name = "api-key")
public class OpenAiService extends OpenAiCompatibleService {
    
    private static final Logger logger = LoggerFactory.getLogger(OpenAiService.class);
    
    // OpenAI API constants
    private static final String DEFAULT_BASE_URL = "https://api.openai.com/v1";
    private static final String DEFAULT_MODEL = "gpt-4o-mini"; // Cost-effective model with vision
    private static final String VISION_MODEL = "gpt-4o"; // High-performance vision model
    private static final String REASONING_MODEL = "o1-preview"; // Reasoning model
    
    private final AiConfigurationProperties.OpenAiConfig config;

    public OpenAiService(AiConfigurationProperties aiConfig) {
        super(
            "openai",
            extractApiKey(aiConfig),
            extractBaseUrl(aiConfig),
            extractModel(aiConfig)
        );
        
        this.config = aiConfig != null ? aiConfig.openai() : null;
        
        if (isAvailable()) {
            logger.info("✅ OpenAI service initialized successfully");
            logger.info("📋 Using model: {} at {}", extractModel(aiConfig), extractBaseUrl(aiConfig));
            logger.info("🔧 Capabilities: Text generation, Image analysis, System prompts");
        } else {
            logger.warn("❌ OpenAI service not available - check API key configuration");
        }
    }
    
    private static String extractApiKey(AiConfigurationProperties aiConfig) {
        return aiConfig != null && aiConfig.openai() != null ? aiConfig.openai().apiKey() : null;
    }
    
    private static String extractBaseUrl(AiConfigurationProperties aiConfig) {
        if (aiConfig != null && aiConfig.openai() != null && aiConfig.openai().baseUrl() != null) {
            return aiConfig.openai().baseUrl();
        }
        return DEFAULT_BASE_URL;
    }
    
    private static String extractModel(AiConfigurationProperties aiConfig) {
        if (aiConfig != null && aiConfig.openai() != null && aiConfig.openai().model() != null) {
            return aiConfig.openai().model();
        }
        return DEFAULT_MODEL;
    }
    
    /**
     * Check if OpenAI reasoning model is configured.
     */
    public boolean isReasoningModel() {
        return REASONING_MODEL.equals(model) || model.startsWith("o1-");
    }
    
    /**
     * Get the current model being used.
     */
    public String getCurrentModel() {
        return model;
    }
    
    /**
     * Check if this is a vision-capable model.
     */
    public boolean isVisionModel() {
        return model.contains("gpt-4o") || model.contains("gpt-4-vision") || 
               model.equals("gpt-4-turbo") || model.equals(DEFAULT_MODEL);
    }
    
    /**
     * Check if this is a standard chat model.
     */
    public boolean isChatModel() {
        return !isReasoningModel();
    }
    
    @Override
    public boolean supportsImageAnalysis() {
        // OpenAI vision models support image analysis
        return isVisionModel();
    }
    
    @Override
    public AiProviderCapabilities getCapabilities() {
        return AiProviderCapabilities.builder()
                .supportsText(true)
                .supportsImage(supportsImageAnalysis())
                .supportsSystemPrompts(true)
                .supportsStreaming(true) // OpenAI supports streaming
                .supportsFunctionCalling(true) // OpenAI supports function calling
                .build();
    }
    
    /**
     * Get OpenAI-specific model information.
     */
    public String getModelInfo() {
        return switch (model) {
            case DEFAULT_MODEL -> "GPT-4o Mini - Cost-effective model with vision capabilities";
            case VISION_MODEL -> "GPT-4o - High-performance model with advanced vision capabilities";
            case REASONING_MODEL -> "o1-preview - Advanced reasoning and problem-solving model";
            case "gpt-4-turbo" -> "GPT-4 Turbo - Fast and capable model with vision support";
            case "gpt-3.5-turbo" -> "GPT-3.5 Turbo - Fast and efficient text model";
            default -> {
                if (model.startsWith("gpt-4")) {
                    yield "GPT-4 Model: " + model + " - Advanced language model";
                } else if (model.startsWith("gpt-3.5")) {
                    yield "GPT-3.5 Model: " + model + " - Efficient language model";
                } else if (model.startsWith("o1-")) {
                    yield "o1 Model: " + model + " - Reasoning-focused model";
                } else {
                    yield "OpenAI Model: " + model;
                }
            }
        };
    }
    
    /**
     * Check if the service has a valid configuration.
     */
    public boolean hasValidConfig() {
        return config != null && config.hasApiKey();
    }
    
    /**
     * Get configuration details for debugging.
     */
    public String getConfigInfo() {
        if (config == null) {
            return "No OpenAI configuration found";
        }
        
        return String.format("OpenAI Config - API Key: %s, Model: %s, Base URL: %s",
                config.hasApiKey() ? "✓ Configured" : "✗ Missing",
                config.model() != null ? config.model() : "Default (" + DEFAULT_MODEL + ")",
                config.baseUrl() != null ? config.baseUrl() : "Default (" + DEFAULT_BASE_URL + ")"
        );
    }
    
    /**
     * Get recommended model for CAPTCHA solving.
     */
    public static String getRecommendedCaptchaModel() {
        return VISION_MODEL; // GPT-4o for best CAPTCHA solving performance
    }
    
    /**
     * Get cost-effective model for general use.
     */
    public static String getCostEffectiveModel() {
        return DEFAULT_MODEL; // GPT-4o Mini for cost-effective operations
    }
}
