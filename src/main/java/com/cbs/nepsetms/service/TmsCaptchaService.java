package com.cbs.nepsetms.service;

import com.cbs.nepsetms.config.ai.AiConfigurationProperties;
import com.cbs.nepsetms.config.tms.TmsConfigurationProperties;
import com.cbs.nepsetms.exception.custom.TmsAuthenticationException;
import com.cbs.nepsetms.model.enums.TmsAuthStatus;
import com.cbs.nepsetms.service.ai.AiServiceManager;
import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * Service for handling TMS CAPTCHA operations.
 */
@Service
public class TmsCaptchaService {
    
    private static final Logger logger = LoggerFactory.getLogger(TmsCaptchaService.class);
    
    private final TmsHttpClient httpClient;
    private final TmsConfigurationProperties config;
    private final AiServiceManager aiServiceManager;
    private final AiConfigurationProperties aiConfig;

    public TmsCaptchaService(TmsHttpClient httpClient,
                           TmsConfigurationProperties config,
                           AiServiceManager aiServiceManager,
                           AiConfigurationProperties aiConfig) {
        this.httpClient = httpClient;
        this.config = config;
        this.aiServiceManager = aiServiceManager;
        this.aiConfig = aiConfig;
    }
    
    /**
     * Get CAPTCHA ID from the server.
     */
    public Mono<String> getCaptchaId() {
        return httpClient.get("/authApi/captcha/id", JsonNode.class)
                .map(response -> {
                    if (response.has("id")) {
                        var captchaId = response.get("id").asText();
                        logger.debug("Received CAPTCHA ID: {}", captchaId);
                        return captchaId;
                    } else {
                        throw new TmsAuthenticationException(
                                TmsAuthStatus.CAPTCHA_FAILED,
                                "No CAPTCHA ID in response"
                        );
                    }
                })
                .doOnError(error -> logger.error("Failed to get CAPTCHA ID", error));
    }
    
    /**
     * Get CAPTCHA image as byte array.
     */
    public Mono<byte[]> getCaptchaImage(String captchaId) {
        var imageUrl = "/authApi/captcha/image/" + captchaId;
        return httpClient.getRaw(imageUrl)
                .doOnSuccess(imageData -> logger.debug("Retrieved CAPTCHA image, size: {} bytes", imageData.length))
                .doOnError(error -> logger.error("Failed to get CAPTCHA image for ID: {}", captchaId, error));
    }
    
    /**
     * Get both CAPTCHA ID and image in one operation.
     */
    public Mono<CaptchaData> getCaptcha() {
        return getCaptchaId()
                .flatMap(captchaId -> 
                    getCaptchaImage(captchaId)
                            .map(imageData -> new CaptchaData(captchaId, imageData))
                )
                .doOnSuccess(captcha -> logger.info("Successfully retrieved CAPTCHA: {}", captcha.captchaId()))
                .doOnError(error -> logger.error("Failed to retrieve CAPTCHA", error));
    }
    
    /**
     * Solve CAPTCHA using AI service with automatic fallback.
     */
    public Mono<String> solveCaptcha(byte[] captchaImage) {
        return solveCaptchaWithFallback(captchaImage);
    }

    /**
     * Solve CAPTCHA using AI service with automatic fallback to other providers.
     * This method tries multiple AI providers if the primary one fails.
     */
    public Mono<String> solveCaptchaWithFallback(byte[] captchaImage) {
        return aiServiceManager.solveCaptchaWithFallback(captchaImage)
                .doOnSuccess(solution -> logger.info("CAPTCHA solved successfully with fallback: {}", solution))
                .doOnError(error -> logger.error("Failed to solve CAPTCHA using AI with fallback", error))
                .onErrorResume(error -> {
                    logger.warn("All AI CAPTCHA solving attempts failed, manual intervention required");
                    return Mono.error(new TmsAuthenticationException(
                            TmsAuthStatus.CAPTCHA_FAILED,
                            "AI CAPTCHA solving failed with all providers: " + error.getMessage()
                    ));
                });
    }

    /**
     * Solve CAPTCHA using a specific AI provider (without fallback).
     */
    public Mono<String> solveCaptchaWithProvider(byte[] captchaImage, String providerName) {
        return aiServiceManager.solveCaptcha(captchaImage, providerName)
                .doOnSuccess(solution -> logger.info("CAPTCHA solved successfully with {}: {}", providerName, solution))
                .doOnError(error -> logger.error("Failed to solve CAPTCHA using {} provider", providerName, error))
                .onErrorResume(error -> {
                    logger.warn("AI CAPTCHA solving failed with {}, manual intervention required", providerName);
                    return Mono.error(new TmsAuthenticationException(
                            TmsAuthStatus.CAPTCHA_FAILED,
                            "AI CAPTCHA solving failed with " + providerName + ": " + error.getMessage()
                    ));
                });
    }

    /**
     * Get CAPTCHA and attempt to solve it automatically using AI with fallback.
     */
    public Mono<CaptchaResult> getCaptchaAndSolve() {
        return getCaptchaAndSolveWithFallback();
    }

    /**
     * Get CAPTCHA and attempt to solve it automatically using AI with fallback to multiple providers.
     */
    public Mono<CaptchaResult> getCaptchaAndSolveWithFallback() {
        return getCaptcha()
                .flatMap(captcha ->
                    solveCaptchaWithFallback(captcha.imageData())
                            .map(solution -> new CaptchaResult(captcha.captchaId(), captcha.imageData(), solution, true))
                            .onErrorReturn(new CaptchaResult(captcha.captchaId(), captcha.imageData(), null, false))
                )
                .doOnSuccess(result -> {
                    if (result.solved()) {
                        logger.info("CAPTCHA automatically solved using AI with fallback: {}", result.solution());
                    } else {
                        logger.info("CAPTCHA requires manual solving - all AI providers failed");
                    }
                });
    }

    /**
     * Get CAPTCHA and attempt to solve it using a specific AI provider.
     */
    public Mono<CaptchaResult> getCaptchaAndSolveWithProvider(String providerName) {
        return getCaptcha()
                .flatMap(captcha ->
                    solveCaptchaWithProvider(captcha.imageData(), providerName)
                            .map(solution -> new CaptchaResult(captcha.captchaId(), captcha.imageData(), solution, true))
                            .onErrorReturn(new CaptchaResult(captcha.captchaId(), captcha.imageData(), null, false))
                )
                .doOnSuccess(result -> {
                    if (result.solved()) {
                        logger.info("CAPTCHA automatically solved using {}: {}", providerName, result.solution());
                    } else {
                        logger.info("CAPTCHA requires manual solving - {} provider failed", providerName);
                    }
                });
    }

    /**
     * CAPTCHA data record.
     */
    public record CaptchaData(String captchaId, byte[] imageData) {}

    /**
     * CAPTCHA result with optional AI solution.
     */
    public record CaptchaResult(String captchaId, byte[] imageData, String solution, boolean solved) {}
}
