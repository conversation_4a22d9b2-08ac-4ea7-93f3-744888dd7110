spring:
  application:
    name: NepseTms

  # Exclude OpenAI auto-configurations we don't need
  autoconfigure:
    exclude:
      - org.springframework.ai.model.openai.autoconfigure.OpenAiAudioSpeechAutoConfiguration
      - org.springframework.ai.model.openai.autoconfigure.OpenAiAudioTranscriptionAutoConfiguration
      - org.springframework.ai.model.openai.autoconfigure.OpenAiImageAutoConfiguration
      - org.springframework.ai.model.openai.autoconfigure.OpenAiEmbeddingAutoConfiguration

  # Server Configuration
  server:
    port: 8080

  # Jackson Configuration for proper DateTime serialization
  jackson:
    serialization:
      write-dates-as-timestamps: false
    date-format: yyyy-MM-dd'T'HH:mm:ss
    time-zone: UTC

  # Database Configuration
  datasource:
    url: jdbc:mysql://${NEPSE_DB_HOST:localhost}:${NEPSE_DB_PORT:3306}/${NEPSE_DB_NAME:nepse_market}?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=UTC
    username: ${NEPSE_DB_USER:nepse_user}
    password: ${NEPSE_DB_PASSWORD:nepse_password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    
    # Connection Pool Configuration
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000

  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: update
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
        implicit-strategy: org.hibernate.boot.model.naming.ImplicitNamingStrategyLegacyJpaImpl
    show-sql: false
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true
        dialect: org.hibernate.dialect.MySQLDialect

  # Spring Cache Configuration
  cache:
    type: simple

  # Spring AI Configuration
  ai:
    # MCP Server Configuration
    mcp:
      server:
        enabled: true
        name: "NEPSE TMS MCP Server"
        version: "1.0.0"
        type: ASYNC  # Recommended for reactive applications
        transport: WEBFLUX
        instructions: "MCP server providing NEPSE TMS trading tools and market data access"
        sse-message-endpoint: /sse
        capabilities:
          tool: true
          resource: true
          prompt: true
          completion: true
        resource-change-notification: true
        tool-change-notification: true
        prompt-change-notification: true
      
      # MCP Client Configuration for testing our own server (disabled for now)
      client:
        enabled: false
        name: "NEPSE Test Client"
        version: "1.0.0"
        type: ASYNC  # Match server type
        request-timeout: 30s
        sse:
          connections:
            nepse-local:
              url: http://localhost:8080
    
    ollama:
      chat:
        enabled: ${OLLAMA_ENABLED:false}
        options:
          model: ${OLLAMA_MODEL:llava:7b}
          temperature: 0.1
      base-url: ${OLLAMA_BASE_URL:http://localhost:11434}

# NEPSE TMS Configuration
nepse:
  # Enterprise Logging Configuration
  logging:
    enabled: true
    trace-enabled: true
    performance-monitoring-enabled: true
    audit-enabled: true
    default-threshold-ms: 5000
    order-threshold-ms: 2000
    auth-threshold-ms: 3000
    market-data-threshold-ms: 1000
    collateral-threshold-ms: 1500
    portfolio-threshold-ms: 3000
    alert-multiplier: 2.0
    log-sensitive-data: false
    max-stack-trace-depth: 10
    include-mdc: true
    
    json:
      enabled: true
      pretty-print: false
      include-timestamp: true
      include-level: true
      include-logger: true
      include-thread: true
      include-mdc: true
      timestamp-format: "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
    
    kibana:
      enabled: false
      index-pattern: "nepse-tms-logs"
      environment: "${SPRING_PROFILES_ACTIVE:development}"
      application-name: "nepse-tms"
      version: "1.0.0"
    
    metrics:
      enabled: true
      export-to-prometheus: true
      include-jvm-metrics: true
      include-system-metrics: true
      metrics-prefix: "nepse_tms"
  tms:
    base-url-domain: ${NEPSE_DOMAIN:tms49.nepsetms.com.np}
    base-url: https://${nepse.tms.base-url-domain}/tmsapi
    login-page-url: https://${nepse.tms.base-url-domain}/login
    login-url: ${nepse.tms.base-url}/authApi/authenticate
    captcha-id-url: ${nepse.tms.base-url}/authApi/captcha/id
    captcha-image-url: ${nepse.tms.base-url}/authApi/captcha/image/{captchaId}
    username: ${NEPSE_USERNAME:2020112302}
    password: ${NEPSE_PASSWORD:Py40Ni5mJVFIaEE/eTk=}
    max-login-attempts: 3
    session-timeout-hours: 1
    request-timeout-seconds: 30
    max-order-amount: 300000.0
    session-maintenance-interval: ${NEPSE_SESSION_MAINTENANCE_INTERVAL:60000} # 60 seconds
    
    # TMS Error Analysis Configuration
    error-analysis:
      enabled: ${NEPSE_ERROR_ANALYSIS_ENABLED:true}  # Enable/disable error analysis and storage
      log-path: ${NEPSE_ERROR_LOG_PATH:logs/tms-errors.jsonl}  # Path to store error analysis
  
    # Post-login endpoints
    post-login-urls:
      - ${nepse.tms.base-url}/systemoptions
      - ${nepse.tms.base-url}/user/frlist
      - ${nepse.tms.base-url}/dashboard/client/{clientId}
    
    # Target endpoints
    collateral-url: /dashboard/client/collateral/{clientId}
  
  # Company Data Scheduler Configuration
  company-data:
    scheduler:
      enabled: ${NEPSE_COMPANY_SCHEDULER_ENABLED:true}  # Enable/disable company data updates
      initial-delay: ${NEPSE_COMPANY_SCHEDULER_INITIAL_DELAY:PT2M}  # 2 minutes after startup (development)
      fixed-delay: ${NEPSE_COMPANY_SCHEDULER_FIXED_DELAY:PT12H}     # 12 hours between runs (development)
      # For production, switch to cron: "0 0 6 * * *" (daily at 6:00 AM)

  # AI Configuration (configurable provider for CAPTCHA solving)
  ai:
    # Default AI provider (gemini, deepseek, openai, or ollama)
    default-provider: gemini

    # Google Generative AI Configuration
    gemini:
      enabled: true
      api-key: AIzaSyC1oLdMtyqyDHbVqjvILRVn2IaVJafgVlo
      model: gemini-2.0-flash-exp
      base-url: https://generativelanguage.googleapis.com/v1beta

    # DeepSeek AI Configuration (OpenAI-compatible)
    deepseek:
      enabled: ${DEEPSEEK_ENABLED:false}  # Enable via environment variable
      api-key: ${DEEPSEEK_API_KEY:***********************************}  # Set via environment variable
      model: ${DEEPSEEK_MODEL:deepseek-chat}  # deepseek-chat (V3) or deepseek-reasoner (R1)
      base-url: ${DEEPSEEK_BASE_URL:https://api.deepseek.com/v1}

    # OpenAI Configuration
    openai:
      enabled: ${OPENAI_ENABLED:false}  # Enable via environment variable
      api-key: ${OPENAI_API_KEY:}  # Set via environment variable
      model: ${OPENAI_MODEL:gpt-4o-mini}  # gpt-4o-mini, gpt-4o, o1-preview, etc.
      base-url: ${OPENAI_BASE_URL:https://api.openai.com/v1}
    # Note: Ollama configuration is handled by Spring AI auto-configuration above

    # CAPTCHA solving configuration
    captcha-solving:
      temperature: 0.1
      max-tokens: 100
      system-prompt: "You are an expert at solving CAPTCHAs. Analyze the image and return only the text you see, nothing else."
      # Fallback configuration
      enable-fallback: ${CAPTCHA_FALLBACK_ENABLED:true}  # Enable automatic fallback to other providers
      max-retries: ${CAPTCHA_MAX_RETRIES:2}  # Number of retries per provider
      preferred-provider: ${CAPTCHA_PREFERRED_PROVIDER:}  # Preferred provider for CAPTCHA solving

  # WebClient Configuration for HTTP communications
  webclient:
    # TMS Client Configuration
    tms:
      connect-timeout: PT10S
      response-timeout: PT30S
      read-timeout: PT30S
      write-timeout: PT30S
      max-in-memory-size: 2097152  # 2MB for TMS responses
      max-connections: 50
      compression: true
      follow-redirects: true

    # Market Data Client Configuration
    market-data:
      connect-timeout: PT5S
      response-timeout: PT15S
      read-timeout: PT15S
      write-timeout: PT15S
      max-in-memory-size: 1048576  # 1MB for market data
      max-connections: 100
      compression: true
      follow-redirects: false

    # AI Service Client Configuration
    ai-service:
      connect-timeout: PT10S
      response-timeout: PT60S  # AI calls can take longer
      read-timeout: PT60S
      write-timeout: PT30S
      max-in-memory-size: 1048576  # 1MB for AI responses
      max-connections: 20
      compression: true
      follow-redirects: false

    # Generic Client Configuration
    generic:
      connect-timeout: PT10S
      response-timeout: PT30S
      read-timeout: PT30S
      write-timeout: PT30S
      max-in-memory-size: 1048576  # 1MB default
      max-connections: 50
      compression: true
      follow-redirects: true

    # Logging Configuration
    logging:
      enabled: true
      level: INFO
      log-headers: true
      log-body: true
      log-form-data: false
      max-body-length: 1024
      sanitize-headers: true
      sensitive-headers:
        - Authorization
        - Cookie
        - Set-Cookie
        - X-API-Key
        - X-Auth-Token

    # Retry Configuration
    retry:
      enabled: true
      max-attempts: 3
      initial-delay: PT0.5S
      max-delay: PT10S
      multiplier: 2.0
      retryable-status-codes:
        - 500
        - 502
        - 503
        - 504
        - 408
      retryable-exceptions:
        - java.net.ConnectException
        - java.util.concurrent.TimeoutException
        - java.io.IOException

    # Metrics Configuration
    metrics:
      enabled: true
      record-request-size: true
      record-response-size: true
      record-duration: true
      additional-tags:
        - "application:nepse-tms"
        - "environment:development"

  # Market Data Provider Configuration
  market-data:
    providers:
      sharehub:
        enabled: ${SHAREHUB_ENABLED:true}
        name: "ShareHub Nepal"
        base-url: "https://sharehubnepal.com"
        timeout-seconds: 30
        rate-limit-per-minute: 60
        capabilities:
          - LIVE_MARKET_DATA
          - MARKET_HOLIDAYS
          - MARKET_STATUS
          - COMPANY_LIST
      sharesansar:
        enabled: ${SHARESANSAR_ENABLED:false}
        name: "ShareSansar"
        base-url: "https://sharesansar.com"
        timeout-seconds: 30
        rate-limit-per-minute: 60
        capabilities:
          - LIVE_MARKET_DATA
          - HISTORICAL_DATA
          - COMPANY_LIST
    
    # Data sync configuration
    sync:
      companies:
        enabled: true
        schedule: "0 0 2 * * ?"  # Daily at 2 AM
        batch-size: 50
      holidays:
        enabled: true
        schedule: "0 0 3 * * SUN"  # Weekly on Sunday at 3 AM
        batch-size: 20
        years-ahead: 2  # Sync current year + 2 years ahead
      cleanup:
        enabled: true
        schedule: "0 0 4 1 * ?"  # Monthly on 1st at 4 AM
        holiday-retention-years: 5
    
    # Cache configuration
    cache:
      live-data-ttl-seconds: 30
      company-list-ttl-minutes: 60
      holiday-list-ttl-hours: 24

# OpenAPI/Swagger Configuration
springdoc:
  api-docs:
    path: /api-docs
    enabled: true
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
    operationsSorter: method
    tagsSorter: alpha
    tryItOutEnabled: true
    filter: true
    displayRequestDuration: true
    displayOperationId: true
    defaultModelsExpandDepth: 2
    defaultModelExpandDepth: 2
    docExpansion: none
  show-actuator: true
  # Auto-discover all API endpoints, no hardcoded paths
  packages-to-scan: com.cbs.nepsetms.controller
  paths-to-match: /api/**

# Logging Configuration
logging:
  level:
    com.cbs.nepsetms: DEBUG
    org.springframework.web: INFO
    org.springframework.orm.jpa: INFO
    org.hibernate.SQL: INFO
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.ai: DEBUG
    org.springframework.ai.autoconfigure: DEBUG
    org.springframework.ai.ollama: DEBUG
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
