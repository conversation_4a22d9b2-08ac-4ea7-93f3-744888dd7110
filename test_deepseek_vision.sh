#!/bin/bash

# Test script to check if DeepSeek API supports vision/image analysis
# This will help us determine if we can use DeepSeek for CAPTCHA solving

echo "🧪 Testing DeepSeek API Vision Support..."

# Check if API key is set
if [ -z "$DEEPSEEK_API_KEY" ]; then
    echo "❌ DEEPSEEK_API_KEY environment variable not set"
    echo "💡 Please set your DeepSeek API key: export DEEPSEEK_API_KEY='your-key-here'"
    exit 1
fi

echo "✅ API key found, testing vision capabilities..."

# Test 1: Simple text request to verify API works
echo ""
echo "🔍 Test 1: Basic API connectivity test..."
curl -s -X POST "https://api.deepseek.com/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $DEEPSEEK_API_KEY" \
  -d '{
    "model": "deepseek-chat",
    "messages": [
      {"role": "user", "content": "Hello, can you see images?"}
    ],
    "max_tokens": 50
  }' | jq '.'

echo ""
echo "🔍 Test 2: Testing OpenAI-style vision format..."

# Small 1x1 pixel PNG image in base64
TEST_IMAGE="iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg=="

# Test 2: OpenAI-style vision format
curl -s -X POST "https://api.deepseek.com/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $DEEPSEEK_API_KEY" \
  -d "{
    \"model\": \"deepseek-chat\",
    \"messages\": [
      {
        \"role\": \"user\",
        \"content\": [
          {
            \"type\": \"text\",
            \"text\": \"What do you see in this image?\"
          },
          {
            \"type\": \"image_url\",
            \"image_url\": {
              \"url\": \"data:image/png;base64,$TEST_IMAGE\"
            }
          }
        ]
      }
    ],
    \"max_tokens\": 100
  }" | jq '.'

echo ""
echo "🔍 Test 3: Testing simple base64 format..."

# Test 3: Simple base64 format
curl -s -X POST "https://api.deepseek.com/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $DEEPSEEK_API_KEY" \
  -d "{
    \"model\": \"deepseek-chat\",
    \"messages\": [
      {
        \"role\": \"user\",
        \"content\": \"![](data:image/png;base64,$TEST_IMAGE) What do you see in this image?\"
      }
    ],
    \"max_tokens\": 100
  }" | jq '.'

echo ""
echo "🔍 Test 4: Testing potential vision model names..."

# Test different model names that might support vision
VISION_MODELS=("deepseek-vl" "deepseek-vl-chat" "deepseek-vl2" "deepseek-vl2-small" "deepseek-vision")

for model in "${VISION_MODELS[@]}"; do
    echo ""
    echo "🔍 Testing model: $model"
    
    curl -s -X POST "https://api.deepseek.com/chat/completions" \
      -H "Content-Type: application/json" \
      -H "Authorization: Bearer $DEEPSEEK_API_KEY" \
      -d "{
        \"model\": \"$model\",
        \"messages\": [
          {
            \"role\": \"user\",
            \"content\": \"Hello, can you see images?\"
          }
        ],
        \"max_tokens\": 50
      }" | jq '.'
done

echo ""
echo "🎯 Test completed! Check the responses above to see if any vision support was detected."
echo ""
echo "📋 What to look for:"
echo "  ✅ Successful responses that mention image analysis capabilities"
echo "  ❌ Error messages about unsupported content types or models"
echo "  🔍 Any model that doesn't return a 'model not found' error"
